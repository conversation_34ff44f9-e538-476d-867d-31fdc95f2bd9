"""
XPath组件高亮器测试脚本
用于验证高亮功能是否正常工作
"""

import logging
import time
import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

# 导入我们的高亮器
from practical_xpath_highlighter import PracticalXPathHighlighter

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_html():
    """创建测试用的HTML页面"""
    html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XPath组件高亮测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .content {
            padding: 40px;
        }
        .section {
            margin-bottom: 40px;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .form-section {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }
        .button-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }
        .navigation-section {
            background: linear-gradient(135deg, #e0c3fc 0%, #9bb5ff 100%);
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 10px rgba(79, 172, 254, 0.3);
        }
        .btn {
            padding: 12px 25px;
            margin: 10px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }
        .btn-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        .btn-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .nav-menu {
            list-style: none;
            padding: 0;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .nav-menu li {
            background: rgba(255,255,255,0.8);
            border-radius: 8px;
            padding: 10px 20px;
            transition: all 0.3s;
        }
        .nav-menu li:hover {
            background: rgba(255,255,255,1);
            transform: scale(1.05);
        }
        .nav-menu a {
            text-decoration: none;
            color: #333;
            font-weight: bold;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .data-table th, .data-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .data-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }
        .data-table tr:hover {
            background-color: #f5f5f5;
        }
        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 40px;
        }
        .special-component {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1 id="page-title">XPath组件高亮测试页面</h1>
            <p>这个页面包含了各种类型的HTML元素，用于测试XPath定位和高亮功能</p>
        </div>

        <div class="content">
            <!-- 导航区域 -->
            <div class="section navigation-section">
                <h2 id="nav-title">导航菜单</h2>
                <ul class="nav-menu" id="main-navigation">
                    <li><a href="#home" class="nav-link">首页</a></li>
                    <li><a href="#products" class="nav-link">产品</a></li>
                    <li><a href="#services" class="nav-link">服务</a></li>
                    <li><a href="#about" class="nav-link">关于我们</a></li>
                    <li><a href="#contact" class="nav-link">联系我们</a></li>
                </ul>
            </div>

            <!-- 表单区域 -->
            <div class="section form-section">
                <h2 id="form-title">用户信息表单</h2>
                <form id="user-form" action="#" method="post">
                    <div class="form-group">
                        <label for="username">用户名:</label>
                        <input type="text" id="username" name="username" placeholder="请输入用户名" required>
                    </div>
                    <div class="form-group">
                        <label for="email">邮箱:</label>
                        <input type="email" id="email" name="email" placeholder="请输入邮箱地址" required>
                    </div>
                    <div class="form-group">
                        <label for="phone">电话:</label>
                        <input type="tel" id="phone" name="phone" placeholder="请输入电话号码">
                    </div>
                    <div class="form-group">
                        <label for="gender">性别:</label>
                        <select id="gender" name="gender">
                            <option value="">请选择</option>
                            <option value="male">男</option>
                            <option value="female">女</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="message">留言:</label>
                        <textarea id="message" name="message" rows="4" placeholder="请输入您的留言"></textarea>
                    </div>
                </form>
            </div>

            <!-- 按钮区域 -->
            <div class="section button-section">
                <h2 id="button-title">操作按钮</h2>
                <button type="submit" class="btn btn-primary" id="submit-btn">提交表单</button>
                <button type="button" class="btn btn-success" id="save-btn">保存草稿</button>
                <button type="button" class="btn btn-warning" id="reset-btn">重置表单</button>
                <button type="button" class="btn btn-info" id="preview-btn">预览</button>
                <a href="#" class="btn btn-primary" id="download-link">下载文件</a>
            </div>

            <!-- 特殊组件 -->
            <div class="special-component" id="special-widget" data-component-type="widget">
                <h3>特殊组件</h3>
                <p>这是一个具有特殊属性的组件，用于测试复杂的XPath定位</p>
                <div class="widget-content" data-widget-id="12345">
                    <span class="widget-status active">状态: 活跃</span>
                </div>
            </div>

            <!-- 数据表格 -->
            <div class="section">
                <h2 id="table-title">数据表格</h2>
                <table class="data-table" id="data-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>姓名</th>
                            <th>邮箱</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr data-user-id="1">
                            <td>1</td>
                            <td>张三</td>
                            <td><EMAIL></td>
                            <td><span class="status active">活跃</span></td>
                            <td>
                                <button class="btn btn-info edit-btn" data-action="edit">编辑</button>
                                <button class="btn btn-warning delete-btn" data-action="delete">删除</button>
                            </td>
                        </tr>
                        <tr data-user-id="2">
                            <td>2</td>
                            <td>李四</td>
                            <td><EMAIL></td>
                            <td><span class="status inactive">非活跃</span></td>
                            <td>
                                <button class="btn btn-info edit-btn" data-action="edit">编辑</button>
                                <button class="btn btn-warning delete-btn" data-action="delete">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 页脚 -->
        <div class="footer" id="page-footer">
            <p>&copy; 2024 XPath组件高亮测试页面. 保留所有权利.</p>
            <p>这个页面专门用于测试各种XPath表达式和组件定位功能</p>
        </div>
    </div>

    <script>
        // 添加一些交互功能
        document.addEventListener('DOMContentLoaded', function() {
            console.log('测试页面已加载完成');
            
            // 为按钮添加点击事件
            document.querySelectorAll('.btn').forEach(function(btn) {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('按钮被点击:', this.textContent);
                });
            });
        });
    </script>
</body>
</html>
    """
    
    # 保存HTML文件
    html_file = "xpath_test_page.html"
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return os.path.abspath(html_file)

def setup_driver():
    """设置Chrome驱动"""
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--allow-running-insecure-content")
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.maximize_window()
    return driver

def test_xpath_highlighting():
    """测试XPath高亮功能"""
    
    # 创建测试页面
    html_file = create_test_html()
    logger.info(f"创建测试页面: {html_file}")
    
    driver = setup_driver()
    
    try:
        # 打开测试页面
        driver.get(f"file://{html_file}")
        logger.info("已打开测试页面")
        
        # 等待页面加载
        time.sleep(2)
        
        # 创建高亮器
        highlighter = PracticalXPathHighlighter(driver)
        
        # 定义测试组件
        test_components = [
            {
                "label": "页面标题",
                "style": "success",
                "locators": [
                    {"type": "xpath", "value": "//h1[@id='page-title']"},
                    {"type": "id", "value": "page-title"},
                    {"type": "css", "value": "#page-title"}
                ]
            },
            {
                "label": "用户名输入框",
                "style": "info",
                "locators": [
                    {"type": "xpath", "value": "//input[@id='username']"},
                    {"type": "id", "value": "username"},
                    {"type": "css", "value": "input[name='username']"}
                ]
            },
            {
                "label": "提交按钮",
                "style": "warning",
                "locators": [
                    {"type": "xpath", "value": "//button[@id='submit-btn']"},
                    {"type": "id", "value": "submit-btn"},
                    {"type": "css", "value": ".btn-primary"}
                ]
            },
            {
                "label": "导航菜单",
                "style": "info",
                "locators": [
                    {"type": "xpath", "value": "//ul[@id='main-navigation']"},
                    {"type": "id", "value": "main-navigation"},
                    {"type": "css", "value": ".nav-menu"}
                ]
            },
            {
                "label": "特殊组件",
                "style": "success",
                "locators": [
                    {"type": "xpath", "value": "//div[@data-component-type='widget']"},
                    {"type": "css", "value": "[data-component-type='widget']"},
                    {"type": "id", "value": "special-widget"}
                ]
            },
            {
                "label": "数据表格",
                "style": "info",
                "locators": [
                    {"type": "xpath", "value": "//table[@id='data-table']"},
                    {"type": "id", "value": "data-table"},
                    {"type": "css", "value": ".data-table"}
                ]
            },
            {
                "label": "编辑按钮(第一行)",
                "style": "warning",
                "locators": [
                    {"type": "xpath", "value": "//tr[@data-user-id='1']//button[@data-action='edit']"},
                    {"type": "css", "value": "[data-user-id='1'] .edit-btn"}
                ]
            },
            {
                "label": "页脚",
                "style": "success",
                "locators": [
                    {"type": "xpath", "value": "//div[@id='page-footer']"},
                    {"type": "id", "value": "page-footer"},
                    {"type": "css", "value": ".footer"}
                ]
            }
        ]
        
        logger.info("=== 开始XPath高亮测试 ===")
        
        # 执行批量高亮
        results = highlighter.highlight_component_list(test_components)
        
        # 创建结果面板
        highlighter.create_results_panel(results)
        
        # 输出测试结果
        logger.info("=== 测试结果 ===")
        logger.info(f"总计测试组件: {results['total']}")
        logger.info(f"成功高亮: {results['success_count']}")
        logger.info(f"失败: {results['failed_count']}")
        logger.info(f"成功率: {(results['success_count']/results['total']*100):.1f}%")
        
        # 详细结果
        for result in results['results']:
            status = "✅" if result['success'] else "❌"
            logger.info(f"{status} {result['label']}")
            if not result['success']:
                logger.warning(f"   失败原因: {result.get('error', '未知')}")
        
        # 等待用户查看效果
        logger.info("💡 请查看浏览器中的高亮效果")
        logger.info("💡 页面右上角显示了详细的结果面板")
        logger.info("💡 不同类型的组件使用了不同颜色的高亮样式")
        
        input("按回车键清除高亮...")
        
        # 清除高亮
        highlighter.clear_all_highlights()
        logger.info("✅ 已清除所有高亮")
        
        input("按回车键结束测试...")
        
        return results['success_count'] == results['total']
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False
        
    finally:
        driver.quit()
        # 清理测试文件
        try:
            os.remove(html_file)
            logger.info("已清理测试文件")
        except:
            pass

def main():
    """主函数"""
    logger.info("=== XPath组件高亮器测试 ===")
    
    success = test_xpath_highlighting()
    
    if success:
        logger.info("🎉 所有测试通过！")
        print("\n" + "="*50)
        print("🎉 测试成功完成！")
        print("✅ 所有组件都成功找到并高亮显示")
        print("💡 XPath组件高亮功能工作正常")
        print("="*50)
    else:
        logger.error("❌ 部分测试失败")
        print("\n" + "="*50)
        print("⚠️  测试完成，但有部分组件未能成功高亮")
        print("💡 请检查XPath表达式或页面结构")
        print("="*50)
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
