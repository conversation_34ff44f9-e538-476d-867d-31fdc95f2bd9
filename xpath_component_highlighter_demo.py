"""
基于XPath的组件高亮演示
展示如何根据XPath找到页面组件并高亮显示
"""

import logging
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class XPathComponentHighlighter:
    """基于XPath的组件高亮器"""
    
    def __init__(self, driver):
        self.driver = driver
        self.wait = WebDriverWait(driver, 10)
        
        # 高亮样式配置
        self.highlight_styles = {
            "found": {
                "border": "3px solid #28a745",
                "background": "rgba(40, 167, 69, 0.2)",
                "box-shadow": "0 0 15px rgba(40, 167, 69, 0.8)"
            },
            "error": {
                "border": "3px solid #dc3545",
                "background": "rgba(220, 53, 69, 0.2)",
                "box-shadow": "0 0 15px rgba(220, 53, 69, 0.8)"
            }
        }
    
    def highlight_by_xpath(self, xpath: str, label_text: str = None) -> bool:
        """
        根据XPath找到元素并高亮显示
        
        Args:
            xpath: XPath表达式
            label_text: 标签文本
            
        Returns:
            是否成功找到并高亮
        """
        try:
            logger.info(f"正在查找XPath: {xpath}")
            
            # 尝试找到元素
            element = self.driver.find_element(By.XPATH, xpath)
            
            if element:
                # 滚动到元素位置
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                time.sleep(0.5)
                
                # 应用高亮样式
                self.apply_highlight_style(element, "found")
                
                # 添加标签
                if not label_text:
                    label_text = f"找到元素: {xpath[:50]}..."
                
                self.add_label(element, label_text, "found")
                
                logger.info(f"✅ 成功找到并高亮元素: {xpath}")
                return True
            else:
                logger.warning(f"❌ 未找到元素: {xpath}")
                return False
                
        except NoSuchElementException:
            logger.warning(f"❌ XPath未找到元素: {xpath}")
            return False
        except Exception as e:
            logger.error(f"❌ 高亮元素失败: {e}")
            return False
    
    def highlight_multiple_xpaths(self, xpath_list: list) -> dict:
        """
        高亮多个XPath对应的元素
        
        Args:
            xpath_list: XPath列表，每个元素可以是字符串或字典 {"xpath": "...", "label": "..."}
            
        Returns:
            高亮结果统计
        """
        results = {
            "total": len(xpath_list),
            "success": 0,
            "failed": 0,
            "success_xpaths": [],
            "failed_xpaths": []
        }
        
        for i, xpath_item in enumerate(xpath_list):
            if isinstance(xpath_item, str):
                xpath = xpath_item
                label = f"元素 {i+1}"
            else:
                xpath = xpath_item.get("xpath", "")
                label = xpath_item.get("label", f"元素 {i+1}")
            
            if self.highlight_by_xpath(xpath, label):
                results["success"] += 1
                results["success_xpaths"].append(xpath)
            else:
                results["failed"] += 1
                results["failed_xpaths"].append(xpath)
            
            # 添加延迟，让用户看到高亮效果
            time.sleep(1)
        
        return results
    
    def apply_highlight_style(self, element, style_type: str):
        """应用高亮样式"""
        
        style_config = self.highlight_styles.get(style_type, self.highlight_styles["found"])
        
        # 构建样式字符串
        style_parts = []
        for property_name, value in style_config.items():
            style_parts.append(f"{property_name}: {value}")
        
        style_string = "; ".join(style_parts)
        
        # 保存原始样式
        original_style = element.get_attribute("style") or ""
        
        # 应用新样式
        new_style = f"{original_style}; {style_string}; position: relative; z-index: 9999;"
        
        self.driver.execute_script(
            "arguments[0].setAttribute('style', arguments[1]); "
            "arguments[0].setAttribute('data-original-style', arguments[2]);",
            element, new_style, original_style
        )
    
    def add_label(self, element, label_text: str, style_type: str):
        """添加标签"""
        
        # 根据样式类型选择标签颜色
        bg_color = "#28a745" if style_type == "found" else "#dc3545"
        
        label_html = f"""
        <div class="xpath-highlight-label" style="
            position: absolute;
            top: -30px;
            left: 0;
            background: {bg_color};
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            z-index: 10000;
            white-space: nowrap;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        ">
            🎯 {label_text}
        </div>
        """
        
        # 插入标签
        self.driver.execute_script("""
            var element = arguments[0];
            var labelHtml = arguments[1];
            
            // 创建标签容器
            var labelContainer = document.createElement('div');
            labelContainer.innerHTML = labelHtml;
            var label = labelContainer.firstElementChild;
            
            // 插入到元素中
            element.appendChild(label);
            
            // 确保元素有相对定位
            if (getComputedStyle(element).position === 'static') {
                element.style.position = 'relative';
            }
        """, element, label_html)
    
    def clear_all_highlights(self):
        """清除所有高亮"""
        try:
            self.driver.execute_script("""
                // 恢复原始样式
                var highlightedElements = document.querySelectorAll('[data-original-style]');
                highlightedElements.forEach(function(element) {
                    var originalStyle = element.getAttribute('data-original-style');
                    element.setAttribute('style', originalStyle);
                    element.removeAttribute('data-original-style');
                });
                
                // 移除所有标签
                var labels = document.querySelectorAll('.xpath-highlight-label');
                labels.forEach(function(label) {
                    label.remove();
                });
            """)
            
            logger.info("✅ 已清除所有高亮")
            
        except Exception as e:
            logger.error(f"❌ 清除高亮失败: {e}")
    
    def create_summary_panel(self, results: dict):
        """创建结果摘要面板"""
        
        summary_html = f"""
        <div id="xpath-highlight-summary" style="
            position: fixed;
            top: 20px;
            right: 20px;
            width: 300px;
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 10001;
            font-family: Arial, sans-serif;
            font-size: 14px;
        ">
            <div style="
                background: #007bff;
                color: white;
                padding: 12px;
                border-radius: 6px 6px 0 0;
                font-weight: bold;
                text-align: center;
            ">
                🎯 XPath 高亮结果
            </div>
            <div style="padding: 15px;">
                <div style="margin-bottom: 10px;">
                    <span style="color: #28a745; font-weight: bold;">✅ 成功: {results['success']}</span>
                </div>
                <div style="margin-bottom: 10px;">
                    <span style="color: #dc3545; font-weight: bold;">❌ 失败: {results['failed']}</span>
                </div>
                <div style="margin-bottom: 15px;">
                    <span style="color: #6c757d; font-weight: bold;">📊 总计: {results['total']}</span>
                </div>
                <div style="text-align: center;">
                    <button onclick="clearXPathHighlights()" style="
                        background: #dc3545;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 12px;
                        font-weight: bold;
                    ">
                        清除高亮
                    </button>
                </div>
            </div>
        </div>
        
        <script>
            function clearXPathHighlights() {{
                // 清除高亮
                var highlightedElements = document.querySelectorAll('[data-original-style]');
                highlightedElements.forEach(function(element) {{
                    var originalStyle = element.getAttribute('data-original-style');
                    element.setAttribute('style', originalStyle);
                    element.removeAttribute('data-original-style');
                }});
                
                // 移除所有标签
                var labels = document.querySelectorAll('.xpath-highlight-label');
                labels.forEach(function(label) {{
                    label.remove();
                }});
                
                // 移除摘要面板
                var panel = document.getElementById('xpath-highlight-summary');
                if (panel) {{
                    panel.remove();
                }}
            }}
        </script>
        """
        
        try:
            self.driver.execute_script("""
                // 移除已存在的摘要面板
                var existingPanel = document.getElementById('xpath-highlight-summary');
                if (existingPanel) {
                    existingPanel.remove();
                }
                
                // 创建新的摘要面板
                var panel = document.createElement('div');
                panel.innerHTML = arguments[0];
                document.body.appendChild(panel.firstElementChild);
            """, summary_html)
            
            logger.info("✅ 已创建结果摘要面板")
            
        except Exception as e:
            logger.error(f"❌ 创建摘要面板失败: {e}")


def setup_driver():
    """设置Chrome驱动"""
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--allow-running-insecure-content")
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.maximize_window()
    return driver


def demo_xpath_highlighting():
    """演示XPath组件高亮功能"""
    
    driver = setup_driver()
    
    try:
        logger.info("=== XPath 组件高亮演示 ===")
        
        # 打开测试页面（可以是任何网页）
        test_url = "https://www.baidu.com"  # 使用百度作为演示
        driver.get(test_url)
        logger.info(f"已打开页面: {test_url}")
        
        # 等待页面加载
        time.sleep(3)
        
        # 创建高亮器
        highlighter = XPathComponentHighlighter(driver)
        
        # 定义要高亮的XPath列表
        xpath_list = [
            {
                "xpath": "//input[@id='kw']",
                "label": "搜索输入框"
            },
            {
                "xpath": "//input[@id='su']",
                "label": "搜索按钮"
            },
            {
                "xpath": "//a[contains(text(), '新闻')]",
                "label": "新闻链接"
            },
            {
                "xpath": "//div[@id='lg']",
                "label": "百度Logo"
            },
            {
                "xpath": "//div[@class='s_tab']",
                "label": "导航栏"
            }
        ]
        
        logger.info("开始高亮页面元素...")
        
        # 高亮多个元素
        results = highlighter.highlight_multiple_xpaths(xpath_list)
        
        # 创建摘要面板
        highlighter.create_summary_panel(results)
        
        # 输出结果
        logger.info("=== 高亮结果 ===")
        logger.info(f"总计: {results['total']} 个XPath")
        logger.info(f"成功: {results['success']} 个")
        logger.info(f"失败: {results['failed']} 个")
        
        if results['success_xpaths']:
            logger.info("成功高亮的XPath:")
            for xpath in results['success_xpaths']:
                logger.info(f"  ✅ {xpath}")
        
        if results['failed_xpaths']:
            logger.info("失败的XPath:")
            for xpath in results['failed_xpaths']:
                logger.info(f"  ❌ {xpath}")
        
        # 等待用户查看效果
        logger.info("💡 请查看浏览器中的高亮效果")
        logger.info("💡 页面右上角显示了结果摘要面板")
        logger.info("💡 成功找到的元素会有绿色边框和标签")
        
        input("按回车键清除高亮...")
        
        # 清除高亮
        highlighter.clear_all_highlights()
        
        input("按回车键结束演示...")
        
    except Exception as e:
        logger.error(f"演示失败: {e}")
        
    finally:
        driver.quit()


if __name__ == "__main__":
    demo_xpath_highlighting()
