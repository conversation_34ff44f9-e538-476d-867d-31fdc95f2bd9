#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Selenium xpath元素定位和高亮演示脚本
演示如何使用ElementHighlighter类进行元素定位和高亮
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from utils.element_highlighter import ElementHighlighter

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def demo_xpath_highlighting():
    """演示xpath元素定位和高亮功能"""
    
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # 启动浏览器
    driver = webdriver.Chrome(options=chrome_options)
    driver.maximize_window()
    
    try:
        # 创建高亮工具实例
        highlighter = ElementHighlighter(driver, default_timeout=10)
        
        logger.info("=== 开始演示xpath元素定位和高亮功能 ===")
        
        # 打开测试页面（以百度为例）
        logger.info("正在打开测试页面...")
        driver.get("https://www.baidu.com")
        time.sleep(3)
        
        # 演示1: 单个元素高亮
        logger.info("\n--- 演示1: 单个元素高亮 ---")
        
        # 高亮搜索框
        result1 = highlighter.highlight_by_xpath(
            xpath="//input[@id='kw']",
            style_type="success", 
            label_text="搜索输入框",
            duration=0  # 不自动移除
        )
        
        if result1["success"]:
            logger.info(f"✅ 搜索框高亮成功")
            logger.info(f"元素信息: {result1['element_info']}")
        else:
            logger.error(f"❌ 搜索框高亮失败: {result1['message']}")
        
        time.sleep(2)
        
        # 高亮搜索按钮
        result2 = highlighter.highlight_by_xpath(
            xpath="//input[@id='su']",
            style_type="primary",
            label_text="搜索按钮"
        )
        
        if result2["success"]:
            logger.info(f"✅ 搜索按钮高亮成功")
        
        time.sleep(2)
        
        # 演示2: 不同样式的高亮
        logger.info("\n--- 演示2: 不同样式的高亮 ---")
        
        # 尝试高亮不同的元素，使用不同样式
        test_elements = [
            {
                "xpath": "//a[contains(text(), '新闻')]",
                "style": "info",
                "label": "新闻链接"
            },
            {
                "xpath": "//a[contains(text(), '贴吧')]", 
                "style": "warning",
                "label": "贴吧链接"
            },
            {
                "xpath": "//a[contains(text(), '视频')]",
                "style": "error", 
                "label": "视频链接"
            }
        ]
        
        for element_config in test_elements:
            result = highlighter.highlight_by_xpath(
                xpath=element_config["xpath"],
                style_type=element_config["style"],
                label_text=element_config["label"]
            )
            
            if result["success"]:
                logger.info(f"✅ {element_config['label']} 高亮成功")
            else:
                logger.warning(f"⚠️ {element_config['label']} 高亮失败")
            
            time.sleep(1)
        
        # 演示3: 批量高亮
        logger.info("\n--- 演示3: 批量高亮多个元素 ---")
        
        # 清除之前的高亮
        highlighter.clear_all_highlights()
        time.sleep(1)
        
        # 批量高亮导航链接
        nav_xpaths = [
            "//a[contains(text(), '新闻')]",
            "//a[contains(text(), '贴吧')]", 
            "//a[contains(text(), '知道')]",
            "//a[contains(text(), '图片')]",
            "//a[contains(text(), '视频')]"
        ]
        
        batch_result = highlighter.highlight_multiple_elements(
            xpath_list=nav_xpaths,
            style_type="info",
            auto_label=True
        )
        
        logger.info(f"批量高亮结果:")
        logger.info(f"  总计: {batch_result['total']} 个元素")
        logger.info(f"  成功: {batch_result['success_count']} 个")
        logger.info(f"  失败: {batch_result['failed_count']} 个")
        
        time.sleep(3)
        
        # 演示4: 查找并操作元素
        logger.info("\n--- 演示4: 查找并操作元素 ---")
        
        # 清除所有高亮
        highlighter.clear_all_highlights()
        
        # 查找搜索框并输入内容
        search_input = highlighter.find_element_by_xpath("//input[@id='kw']")
        if search_input:
            # 高亮搜索框
            highlighter.highlight_element(search_input, "success", "正在输入...")
            
            # 输入搜索内容
            search_input.clear()
            search_input.send_keys("Selenium自动化测试")
            logger.info("✅ 已在搜索框中输入内容")
            
            time.sleep(2)
            
            # 查找并高亮搜索按钮
            search_button = highlighter.find_element_by_xpath("//input[@id='su']")
            if search_button:
                highlighter.highlight_element(search_button, "primary", "点击搜索")
                time.sleep(1)
                
                # 点击搜索按钮
                search_button.click()
                logger.info("✅ 已点击搜索按钮")
                
                # 等待搜索结果加载
                time.sleep(3)
                
                # 高亮搜索结果
                logger.info("\n--- 高亮搜索结果 ---")
                result_xpaths = [
                    "//h3[@class='t']/a",  # 搜索结果标题
                    "(//h3[@class='t']/a)[2]",  # 第二个结果
                    "(//h3[@class='t']/a)[3]"   # 第三个结果
                ]
                
                for i, xpath in enumerate(result_xpaths):
                    result = highlighter.highlight_by_xpath(
                        xpath=xpath,
                        style_type="warning",
                        label_text=f"搜索结果 {i+1}"
                    )
                    if result["success"]:
                        logger.info(f"✅ 搜索结果 {i+1} 高亮成功")
                    time.sleep(0.5)
        
        # 演示5: 临时高亮（自动消失）
        logger.info("\n--- 演示5: 临时高亮效果 ---")
        
        # 清除所有高亮
        highlighter.clear_all_highlights()
        time.sleep(1)
        
        # 创建临时高亮，3秒后自动消失
        temp_result = highlighter.highlight_by_xpath(
            xpath="//input[@id='kw']",
            style_type="error",
            label_text="临时高亮(3秒后消失)",
            duration=3
        )
        
        if temp_result["success"]:
            logger.info("✅ 临时高亮已设置，将在3秒后自动消失")
        
        # 等待用户观察
        logger.info("\n=== 演示完成 ===")
        logger.info("💡 请观察浏览器中的高亮效果")
        logger.info("📝 高亮功能说明:")
        logger.info("   - success(绿色): 成功状态")
        logger.info("   - primary(蓝色): 主要操作")
        logger.info("   - info(青色): 信息提示") 
        logger.info("   - warning(黄色): 警告状态")
        logger.info("   - error(红色): 错误状态")
        
        # 等待用户输入
        input("\n按回车键清除所有高亮并结束演示...")
        
        # 清除所有高亮
        highlighter.clear_all_highlights()
        logger.info("✅ 已清除所有高亮效果")
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
        
    finally:
        # 关闭浏览器
        time.sleep(2)
        driver.quit()
        logger.info("🔚 演示结束，浏览器已关闭")


def demo_custom_page():
    """演示在自定义页面上的xpath高亮功能"""
    
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.maximize_window()
    
    try:
        highlighter = ElementHighlighter(driver)
        
        logger.info("=== 自定义页面xpath高亮演示 ===")
        
        # 可以替换为您要测试的页面
        test_url = input("请输入要测试的页面URL (直接回车使用默认页面): ").strip()
        if not test_url:
            test_url = "https://www.github.com"
        
        logger.info(f"正在打开页面: {test_url}")
        driver.get(test_url)
        time.sleep(3)
        
        # 交互式xpath测试
        while True:
            print("\n" + "="*50)
            print("xpath高亮测试菜单:")
            print("1. 输入xpath进行高亮测试")
            print("2. 清除所有高亮")
            print("3. 退出")
            print("="*50)
            
            choice = input("请选择操作 (1-3): ").strip()
            
            if choice == "1":
                xpath = input("请输入xpath表达式: ").strip()
                if xpath:
                    style_type = input("请输入样式类型 (success/primary/info/warning/error，默认success): ").strip()
                    if not style_type:
                        style_type = "success"
                    
                    label_text = input("请输入标签文本 (可选): ").strip()
                    if not label_text:
                        label_text = None
                    
                    result = highlighter.highlight_by_xpath(
                        xpath=xpath,
                        style_type=style_type,
                        label_text=label_text
                    )
                    
                    if result["success"]:
                        print(f"✅ 元素高亮成功!")
                        print(f"元素信息: {result['element_info']}")
                    else:
                        print(f"❌ 元素高亮失败: {result['message']}")
                        
            elif choice == "2":
                highlighter.clear_all_highlights()
                print("✅ 已清除所有高亮")
                
            elif choice == "3":
                break
                
            else:
                print("❌ 无效选择，请重新输入")
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
        
    finally:
        driver.quit()
        logger.info("演示结束")


if __name__ == "__main__":
    print("Selenium xpath元素定位和高亮演示")
    print("1. 自动演示 (使用百度页面)")
    print("2. 自定义页面测试")
    
    choice = input("请选择演示模式 (1-2): ").strip()
    
    if choice == "1":
        demo_xpath_highlighting()
    elif choice == "2":
        demo_custom_page()
    else:
        print("无效选择")
