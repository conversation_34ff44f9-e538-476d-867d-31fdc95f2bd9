#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Selenium元素高亮工具类
通过xpath定位元素并设置高亮效果
"""

import time
import logging
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ElementHighlighter:
    """元素高亮工具类"""
    
    def __init__(self, driver, default_timeout=10):
        """
        初始化高亮工具
        
        Args:
            driver: Selenium WebDriver实例
            default_timeout: 默认等待超时时间（秒）
        """
        self.driver = driver
        self.default_timeout = default_timeout
        self.highlighted_elements = []  # 记录已高亮的元素
        
        # 高亮样式配置
        self.highlight_styles = {
            "success": {
                "border": "3px solid #28a745",
                "background": "rgba(40, 167, 69, 0.2)",
                "box-shadow": "0 0 15px rgba(40, 167, 69, 0.8)"
            },
            "warning": {
                "border": "3px solid #ffc107", 
                "background": "rgba(255, 193, 7, 0.2)",
                "box-shadow": "0 0 15px rgba(255, 193, 7, 0.8)"
            },
            "error": {
                "border": "3px solid #dc3545",
                "background": "rgba(220, 53, 69, 0.2)", 
                "box-shadow": "0 0 15px rgba(220, 53, 69, 0.8)"
            },
            "info": {
                "border": "3px solid #17a2b8",
                "background": "rgba(23, 162, 184, 0.2)",
                "box-shadow": "0 0 15px rgba(23, 162, 184, 0.8)"
            },
            "primary": {
                "border": "3px solid #007bff",
                "background": "rgba(0, 123, 255, 0.2)",
                "box-shadow": "0 0 15px rgba(0, 123, 255, 0.8)"
            }
        }
    
    def find_element_by_xpath(self, xpath, timeout=None):
        """
        通过xpath查找元素
        
        Args:
            xpath: xpath表达式
            timeout: 等待超时时间，默认使用类初始化时的timeout
            
        Returns:
            WebElement对象，如果未找到返回None
        """
        if timeout is None:
            timeout = self.default_timeout
            
        try:
            logger.info(f"正在查找元素，xpath: {xpath}")
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((By.XPATH, xpath))
            )
            logger.info(f"✅ 成功找到元素: {xpath}")
            return element
        except TimeoutException:
            logger.warning(f"❌ 查找元素超时: {xpath}")
            return None
        except Exception as e:
            logger.error(f"❌ 查找元素时发生错误: {xpath}, 错误: {e}")
            return None
    
    def highlight_element(self, element, style_type="success", label_text=None, duration=0):
        """
        高亮显示元素
        
        Args:
            element: WebElement对象
            style_type: 高亮样式类型 (success, warning, error, info, primary)
            label_text: 标签文本
            duration: 高亮持续时间（秒），0表示不自动移除
            
        Returns:
            bool: 是否成功高亮
        """
        try:
            # 滚动到元素位置
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
            time.sleep(0.5)
            
            # 获取样式配置
            style_config = self.highlight_styles.get(style_type, self.highlight_styles["success"])
            
            # 保存原始样式
            original_style = element.get_attribute("style") or ""
            
            # 构建新样式
            style_parts = []
            for property_name, value in style_config.items():
                style_parts.append(f"{property_name}: {value}")
            
            style_string = "; ".join(style_parts)
            new_style = f"{original_style}; {style_string}; position: relative; z-index: 9999;"
            
            # 应用高亮样式
            self.driver.execute_script(
                "arguments[0].setAttribute('style', arguments[1]); "
                "arguments[0].setAttribute('data-original-style', arguments[2]); "
                "arguments[0].setAttribute('data-highlight-type', arguments[3]);",
                element, new_style, original_style, style_type
            )
            
            # 添加标签（如果提供）
            if label_text:
                self._add_label(element, label_text, style_type)
            
            # 记录已高亮的元素
            self.highlighted_elements.append(element)
            
            logger.info(f"✅ 元素高亮成功，样式: {style_type}")
            
            # 如果设置了持续时间，则自动移除高亮
            if duration > 0:
                time.sleep(duration)
                self.remove_highlight(element)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 元素高亮失败: {e}")
            return False
    
    def highlight_by_xpath(self, xpath, style_type="success", label_text=None, timeout=None, duration=0):
        """
        通过xpath查找并高亮元素
        
        Args:
            xpath: xpath表达式
            style_type: 高亮样式类型
            label_text: 标签文本
            timeout: 查找超时时间
            duration: 高亮持续时间（秒）
            
        Returns:
            dict: 操作结果
        """
        # 查找元素
        element = self.find_element_by_xpath(xpath, timeout)
        
        if element is None:
            return {
                "success": False,
                "message": f"未找到元素: {xpath}",
                "xpath": xpath,
                "element": None
            }
        
        # 高亮元素
        highlight_success = self.highlight_element(element, style_type, label_text, duration)
        
        return {
            "success": highlight_success,
            "message": "元素高亮成功" if highlight_success else "元素高亮失败",
            "xpath": xpath,
            "element": element,
            "element_info": self._get_element_info(element)
        }
    
    def _add_label(self, element, label_text, style_type):
        """为元素添加标签"""
        try:
            # 获取样式对应的颜色
            color_map = {
                "success": "#28a745",
                "warning": "#ffc107", 
                "error": "#dc3545",
                "info": "#17a2b8",
                "primary": "#007bff"
            }
            
            color = color_map.get(style_type, "#28a745")
            
            # 创建标签的JavaScript代码
            js_code = f"""
            var label = document.createElement('div');
            label.innerHTML = '{label_text}';
            label.className = 'xpath-highlight-label';
            label.style.cssText = `
                position: absolute;
                top: -25px;
                left: 0;
                background: {color};
                color: white;
                padding: 2px 8px;
                border-radius: 3px;
                font-size: 12px;
                font-weight: bold;
                z-index: 10000;
                white-space: nowrap;
                box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            `;
            
            // 确保元素有相对定位
            if (arguments[0].style.position !== 'relative' && arguments[0].style.position !== 'absolute') {{
                arguments[0].style.position = 'relative';
            }}
            
            arguments[0].appendChild(label);
            """
            
            self.driver.execute_script(js_code, element)
            
        except Exception as e:
            logger.warning(f"添加标签失败: {e}")
    
    def _get_element_info(self, element):
        """获取元素信息"""
        try:
            return {
                "tag_name": element.tag_name,
                "text": element.text[:100] if element.text else "",
                "location": element.location,
                "size": element.size,
                "is_displayed": element.is_displayed(),
                "is_enabled": element.is_enabled()
            }
        except Exception as e:
            logger.warning(f"获取元素信息失败: {e}")
            return {}
    
    def remove_highlight(self, element):
        """移除单个元素的高亮"""
        try:
            # 恢复原始样式
            original_style = element.get_attribute("data-original-style") or ""
            self.driver.execute_script(
                "arguments[0].setAttribute('style', arguments[1]); "
                "arguments[0].removeAttribute('data-original-style'); "
                "arguments[0].removeAttribute('data-highlight-type');",
                element, original_style
            )
            
            # 移除标签
            self.driver.execute_script("""
                var labels = arguments[0].querySelectorAll('.xpath-highlight-label');
                labels.forEach(function(label) {
                    label.remove();
                });
            """, element)
            
            # 从记录中移除
            if element in self.highlighted_elements:
                self.highlighted_elements.remove(element)
                
            logger.info("✅ 移除元素高亮成功")
            
        except Exception as e:
            logger.error(f"❌ 移除元素高亮失败: {e}")
    
    def clear_all_highlights(self):
        """清除所有高亮"""
        try:
            # 使用JavaScript批量清除
            self.driver.execute_script("""
                // 恢复所有高亮元素的原始样式
                var highlightedElements = document.querySelectorAll('[data-original-style]');
                highlightedElements.forEach(function(element) {
                    var originalStyle = element.getAttribute('data-original-style');
                    element.setAttribute('style', originalStyle);
                    element.removeAttribute('data-original-style');
                    element.removeAttribute('data-highlight-type');
                });
                
                // 移除所有标签
                var labels = document.querySelectorAll('.xpath-highlight-label');
                labels.forEach(function(label) {
                    label.remove();
                });
            """)
            
            # 清空记录
            self.highlighted_elements.clear()
            
            logger.info("✅ 已清除所有高亮")
            
        except Exception as e:
            logger.error(f"❌ 清除所有高亮失败: {e}")
    
    def highlight_multiple_elements(self, xpath_list, style_type="success", auto_label=True):
        """
        批量高亮多个元素
        
        Args:
            xpath_list: xpath表达式列表
            style_type: 高亮样式类型
            auto_label: 是否自动添加标签
            
        Returns:
            dict: 批量操作结果
        """
        results = []
        success_count = 0
        
        for i, xpath in enumerate(xpath_list):
            label_text = f"元素 {i+1}" if auto_label else None
            result = self.highlight_by_xpath(xpath, style_type, label_text)
            results.append(result)
            
            if result["success"]:
                success_count += 1
        
        return {
            "total": len(xpath_list),
            "success_count": success_count,
            "failed_count": len(xpath_list) - success_count,
            "results": results
        }
