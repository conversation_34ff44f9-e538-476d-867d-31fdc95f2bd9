
import pytest
from time import sleep
from test_case.core_link.product_center.base import BaseTestCase
import os
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
import time
from unittest import skip

from test_case.core_link.product_center.base_config import UI_ITEMS_ID


class TestSytCustomer(BaseTestCase):

    @pytest.mark.p0
    @pytest.mark.smoke
    def test_main_photo(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 类目选择页
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        self.click("//span[contains(text(),'新增商品')]")
        self.driver.maximize_window()
        sleep(2)

        # 上传非1：1主图，自动裁剪
        path = os.path.abspath("..")
        self.choose_file('input[type="file"]', path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg')
        time.sleep(3)
        self.click("//span[contains(text(),'确认裁剪')]")
        time.sleep(3)
        self.click("//span[contains(text(),'应用图片')]")
        time.sleep(2)

        #预览图片
        sleep(3)
        self.click("//*[text()='头图']")
        self.click("//*[text()='预览']")
        sleep(2)
        self.driver.find_elements(By.XPATH, "//span[@class='goods-modal-close-x']")[0].click()

        #删除图片
        self.click("//*[text()='头图']")
        self.click("//span[@class='anticon anticon-close-circle close-icon___QTTw1']")

        #上传1：1图片，不裁剪
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[0].send_keys(path + '/kwaishopuiautotest/test_data/img/white.png')
        sleep(3)
        self.assert_equal(len(self.find_elements("//div[@class='replace___KuPlf']")), 1)

        #上传jpeg图片
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[1].send_keys(path + '/kwaishopuiautotest/test_data/img/allow_jpeg.jpeg')
        time.sleep(3)
        self.click("//span[contains(text(),'确认裁剪')]")
        time.sleep(5)
        self.click("//span[contains(text(),'应用图片')]")
        time.sleep(2)
        self.assert_equal(len(self.find_elements("//div[@class='replace___KuPlf']")), 2)
        sleep(3)

        #上传不符合webp\bmp格式图片，拦截
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[2].send_keys(path + '/kwaishopuiautotest/test_data/img/nowebp.webp')
        self.assert_text('不是合法的图片类型!', "//span[contains(text(),'不是合法的图片类型!')]")
        sleep(3)
        self.assert_equal(len(self.find_elements("//div[@class='replace___KuPlf']")), 2)

        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[2].send_keys(path + '/kwaishopuiautotest/test_data/img/nobmp.bmp')
        self.assert_text('不是合法的图片类型!', "//span[contains(text(),'不是合法的图片类型!')]")
        sleep(3)
        self.assert_equal(len(self.find_elements("//div[@class='replace___KuPlf']")), 2)

        #上传大于2M的图片
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[2].send_keys(path + '/kwaishopuiautotest/test_data/img/big.jpg')
        self.assert_text('文件大小不能超过2MB!', "//span[contains(text(),'文件大小不能超过2MB!')]")
        sleep(3)
        self.assert_equal(len(self.find_elements("//div[@class='replace___KuPlf']")), 2)

    @pytest.mark.p0
    # @pytest.mark.smoke # 其他case已覆盖
    def test_detail_photo(self):
        self.add_page("其他钟表","钟表类目","search")
        # 移动到部分
        elee = self.driver.find_element(By.XPATH, "//p[contains(text(),'商品图文')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        sleep(1)
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[-1].send_keys(path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg')
        sleep(5)
        self.assert_text('继续上传', "//span[contains(text(),'继续上传')]")

    @pytest.mark.p1
    def test_industryMaterial_photo(self):
        """
        行业素材图	上传成功
        指定类目：营养健康>传统滋补>滋补养生原料>豆蔻（特定类目）
        """
        self.add_page("豆蔻","","search", account="product_account_2")
        #移动到部分
        elee = self.driver.find_element(By.XPATH, "//span[contains(text(),'暂未编辑商品详情图')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        sleep(3)

        # 上传非1:1 弹窗裁剪
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[6].send_keys(path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg')
        sleep(3)
        self.assert_element('//div[contains(text(),"图片裁剪")]')
        self.logger.info("成功打开图片裁剪弹窗")

        self.driver.find_elements(By.XPATH, "//span[contains(text(),'确 定')]")[-1].click()
        sleep(3)
        # 0630变更：行业素材图 的提示，放量后，按照产品诉求，不强拦截了 wangzifan
        # self.assert_element('//div[contains(text(),"当前图片非营养成分表")]')

    @pytest.mark.p1
    def test_prop_photo(self):
        self.add_page("autotest-类目属性校验","属性类目","search")
        # 处理属性折叠
        ele = self.driver.find_element(By.XPATH, '//span[contains(text(),"重要属性")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(1)
        if self.is_element_visible('//span[contains(text(),"展开查看更多属性")]'):
            self.click('//span[contains(text(),"展开查看更多属性")]')
            sleep(1)

        # 移动到属性图部分
        elee = self.driver.find_element(By.XPATH, '//label[contains(text(),"适用肤质")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        sleep(5)
        path = os.path.abspath("..")
        print(path)
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[0].send_keys(path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg')
        sleep(3)

        #预览属性图
        self.driver.find_elements(By.XPATH, "//div[@class='replace___kliEq']")[0].click()
        sleep(3)
        self.assert_element("//p[contains(text(),'预览')]")
        self.click("//p[contains(text(),'预览')]")
        sleep(3)
        self.driver.find_elements(By.XPATH,"//span[@class='goods-modal-close-x']" )[-1].click()
        sleep(3)

        # 裁剪框唤起
        self.driver.find_elements(By.XPATH, "//div[@class='replace___kliEq']")[0].click()
        sleep(1)
        self.click("//p[contains(text(),'裁剪')]")
        sleep(3)
        self.assert_text('图片裁剪', "//div[contains(text(),'图片裁剪')]")
        self.driver.find_elements(By.XPATH,"//span[@class='goods-modal-close-x']" )[-1].click()
        sleep(3)

        # 素材中心替换唤起
        self.driver.find_elements(By.XPATH, "//div[@class='replace___kliEq']")[0].click()
        sleep(1)
        self.click("//p[contains(text(),'素材中心替换')]")
        sleep(3)
        self.assert_text('图片素材', "//div[contains(text(),'图片素材')]")
        self.driver.find_elements(By.XPATH,"//span[@class='goods-modal-close-x']" )[-1].click()
        sleep(3)

    @skip("远程调试后放开")
    def test_batch_upload(self):
        self.add_page("autotest-类目属性校验", "属性类目", "search")
        elee = self.driver.find_element(By.XPATH, '//div[@id="itemRelease_detailImageUrls"]')
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)

        path1 = os.path.abspath("..")
        path = path1 + '/kwaishopuiautotest/test_data/img'
        file_name_list = os.listdir(path)
        path_list = [os.path.join(path, x) for x in file_name_list]
        path_list_final = [s for s in path_list if "detail" in s]
        path_split_by_newline = '\n'.join(path_list_final)
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[3].send_keys(path_split_by_newline)
        sleep(20)
        self.assert_no_404_errors()
        sleep(3)
        self.assert_equal(len(self.find_elements("//img[@class='sortable']")), 53) #包含主图、素材图
        sleep(10)

    @skip("代码fix")
    def test_threequarter_upload(self):
        self.add_page("autotest-渠道校验-可售","可售类目", "search")
        sleep(5)
        elee = self.driver.find_element(By.XPATH, '//div[contains(text(),"上传图片")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        sleep(3)

        #3：4预填
        self.assert_text('预填', "//span[contains(text(),'预填')]")

        #一键只能生成点击
        self.click("//span[contains(text(),'一键智能生成')]")
        self.assert_text('图片生成中', "//span[contains(text(),'图片生成中')]")
        sleep(10)
        self.assert_text('预填', "//span[contains(text(),'预填')]")

        #预览
        self.click("//span[contains(text(),'预填')]")
        self.click("//p[contains(text(),'预览')]")
        sleep(3)
        self.driver.find_elements(By.XPATH,"//span[@class='goods-modal-close-x']" )[-1].click()
        sleep(3)

        #裁剪框唤起
        self.click("//span[contains(text(),'预填')]")
        self.click("//p[contains(text(),'裁剪')]")
        sleep(3)
        self.assert_text('图片裁剪', "//div[contains(text(),'图片裁剪')]")
        self.driver.find_elements(By.XPATH,"//span[@class='goods-modal-close-x']" )[-1].click()
        sleep(3)

        #素材中心替换唤起
        self.click("//span[contains(text(),'预填')]")
        self.click("//p[contains(text(),'素材中心替换')]")
        sleep(3)
        self.assert_text('图片素材', "//div[contains(text(),'图片素材')]")
        self.driver.find_elements(By.XPATH,"//span[@class='goods-modal-close-x']" )[-1].click()
        sleep(3)


        # #3：4图删除后重新上传并裁剪
        self.driver.find_elements(By.XPATH,"//span[@class='anticon anticon-close-circle close-icon___yakWH']")[1].click()
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[2].send_keys(path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg')
        sleep(3)
        self.assert_text('图片裁剪', "//div[contains(text(),'图片裁剪')]")
        self.driver.find_elements(By.XPATH,"//span[contains(text(),'确 定')]")[1].click()
        sleep(5)
        self.assert_equal(len(self.driver.find_elements(By.XPATH,"//span[contains(text(),'3:4主图')]")),2)

        #3：4图删除后重新上传，无需裁剪
        self.driver.find_elements(By.XPATH, "//span[@class='anticon anticon-close-circle close-icon___yakWH']")[1].click()
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[2].send_keys(path + '/kwaishopuiautotest/test_data/img/threefour.jpg')
        sleep(3)
        self.assert_equal(len(self.find_elements("//div[@class='replace___kliEq']")), 4)
        sleep(3)

        # 3：4图删除后上传小于375*500，上传失败
        self.driver.find_elements(By.XPATH, "//span[@class='anticon anticon-close-circle close-icon___yakWH']")[1].click()
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[2].send_keys(path + '/kwaishopuiautotest/test_data/img/small.png')
        # self.assert_text('长图像素不低于375 * 500[403]', "//span[contains(text(),'长图像素不低于375 * 500[403]')]")
        # sleep(3)
        self.assert_equal(len(self.find_elements("//div[@class='replace___kliEq']")), 3)
        sleep(3)

    @pytest.mark.p1
    def test_white_upload(self):
        self.add_page("autotest-渠道校验-可售","可售类目", "search")
        sleep(5)
        elee = self.driver.find_element(By.XPATH, '//span[contains(text(),"开启后，将根据已填写的商品信息自动生成「商品卖点图」并在商品详情页展示")]')
        self.driver.execute_script("arguments[0].scrollIntoView(false);", elee)
        sleep(3)
        input_index = 4

        #预览
        self.driver.find_elements(By.XPATH,'//span[contains(text(),"预填")]')[1].click()
        self.click("//p[contains(text(),'预览')]")
        sleep(3)
        self.driver.find_elements(By.XPATH,"//span[@class='goods-modal-close-x']" )[-1].click()
        sleep(3)

        #裁剪框唤起
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"预填")]')[1].click()
        self.click("//p[contains(text(),'图片编辑工具')]")
        sleep(3)
        self.click("//span[contains(text(),'裁剪')]")
        time.sleep(3)
        self.click("//span[contains(text(),'确认裁剪')]")
        time.sleep(3)
        self.click("//span[contains(text(),'应用图片')]")
        sleep(3)

        #素材中心替换唤起
        self.driver.find_elements(By.XPATH,'//span[contains(text(),"白底图")]')[-1].click()
        self.click("//p[contains(text(),'素材中心替换')]")
        sleep(3)
        self.assert_text('图片素材', "//div[contains(text(),'图片素材')]")
        self.driver.find_elements(By.XPATH,"//span[@class='goods-modal-close-x']" )[-1].click()
        sleep(3)

        # 抠图,使用抠图
        self.driver.find_elements(By.XPATH,'//span[contains(text(),"白底图")]')[-1].click()
        self.click("//p[contains(text(),'图片编辑工具')]")
        sleep(3)
        self.click("//span[contains(text(),'立即生成')]")
        time.sleep(3)
        self.click("//span[contains(text(),'应用图片')]")
        sleep(3)

        # 白底图删除后重新上传
        self.driver.find_elements(By.XPATH,"//span[@class='anticon anticon-close-circle close-icon___QTTw1']")[1].click()
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[input_index].send_keys(path + '/kwaishopuiautotest/test_data/img/white.png')
        sleep(3)

        #上传小于480像素的图片
        self.driver.find_elements(By.XPATH, "//span[@class='anticon anticon-close-circle close-icon___QTTw1']")[1].click()
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[input_index].send_keys(path + '/kwaishopuiautotest/test_data/img/small.png')
        # self.assert_text('请上传像素', "//span[contains(text(),'请上传像素")
        sleep(1)
        # self.assert_element('//span[contains(text(),"请上传像素")]')
        self.assert_element('//span[contains(text(),"图片宽度不能低于480px!")]')

    def test_transparent_upload(self):
        self.add_page("autotest-渠道校验-可售","可售类目", "search")
        sleep(5)
        elee = self.driver.find_element(By.XPATH, '//span[contains(text(),"编辑商品详情图，提升商品转化率")]')
        self.driver.execute_script("arguments[0].scrollIntoView(false);", elee)
        sleep(1)
        input_index = 5

        #预览
        self.driver.find_elements(By.XPATH,'//span[contains(text(),"预填")]')[2].click()
        self.click("//p[contains(text(),'预览')]")
        sleep(1)
        self.driver.find_elements(By.XPATH,"//span[@class='goods-modal-close-x']" )[-1].click()
        sleep(1)

        #裁剪框唤起
        self.driver.find_elements(By.XPATH,'//span[contains(text(),"预填")]')[2].click()
        self.click("//p[contains(text(),'图片编辑工具')]")
        sleep(3)
        self.click("//span[contains(text(),'裁剪')]")
        time.sleep(3)
        self.click("//span[contains(text(),'确认裁剪')]")
        time.sleep(3)
        self.click("//span[contains(text(),'应用图片')]")
        sleep(3)

        #素材中心替换唤起
        self.driver.find_elements(By.XPATH,'//span[contains(text(),"透明图")]')[-1].click()
        self.click("//p[contains(text(),'素材中心替换')]")
        sleep(3)
        self.assert_text('图片素材', "//div[contains(text(),'图片素材')]")
        self.driver.find_elements(By.XPATH,"//span[@class='goods-modal-close-x']" )[-1].click()
        sleep(3)

        # 透明图删除后重新上传
        self.driver.find_elements(By.XPATH,"//span[@class='anticon anticon-close-circle close-icon___QTTw1']")[2].click()
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[input_index].send_keys(
            path + '/kwaishopuiautotest/test_data/img/white.png')
        sleep(5)
        # 上传非png图片
        self.driver.find_elements(By.XPATH, "//span[@class='anticon anticon-close-circle close-icon___QTTw1']")[
            2].click()
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[input_index].send_keys(
            path + '/kwaishopuiautotest/test_data/img/match_shopping.jpg')
        sleep(1)
        self.assert_element("//span[contains(text(),'不是合法的图片类型!只允许上传png')]")

    @skip("UI变动，调试后放开")
    def test_qualifition_upload(self):
        self.add_page("化妆棉","资质类目", "search")
        sleep(5)
        elee = self.driver.find_element(By.XPATH, '//span[contains(text(),"品牌")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        sleep(3)
        self.click('//label[contains(text(),"是否进口")]/../../../../div[2]/div/div/div/div/div/div/div[2]')
        sleep(5)
        elee_2 = self.driver.find_element(By.XPATH, '//span[contains(text(),"商品资质")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", elee_2)
        sleep(5)

        #上传一个资质图片
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[2].send_keys(path + '/kwaishopuiautotest/test_data/img/white.png')
        sleep(5)

        #预览资质图片
        self.click('//div[@class="clearfix"]/div/div/img')
        sleep(2)
        self.click('//div[contains(text(),"预览")]')
        sleep(2)
        self.driver.find_elements(By.XPATH, '//span[@class="anticon anticon-system-close-medium-line goods-modal-close-icon"]')[1].click()
        #关闭预览窗口
        self.click('//div[@class="clearfix"]/div/div/img')
        sleep(2)
        self.click('//span[@aria-label="close-circle"]')

    @pytest.mark.p1
    def test_brand_upload(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        time.sleep(4)
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")

        self.open_url("https://s.kwaixiaodian.com/zone/goods/brand/apply?from=edit") # todo待处理
        sleep(5)
        self.refresh()
        sleep(2)

        elee = self.driver.find_element(By.XPATH, '//label[contains(text(),"所属类目")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)

        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[0].send_keys(path + '/kwaishopuiautotest/test_data/img/white.png')
        sleep(3)


        #点击预览
        self.click('//img[@class="sortable"]')
        sleep(2)
        self.click('//div[contains(text(),"预览")]')
        sleep(2)
        self.click('//span[@class="ant-modal-close-x"]')
        sleep(3)

    @pytest.mark.p0
    @pytest.mark.smoke
    def test_size_add_upload(self):

        self.add_page("autotest-渠道校验-可售", "可售类目", "search")
        elee = self.driver.find_element(By.XPATH, "//span[contains(text(),'商品规格')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)

        sleep(3)
        self.driver.find_elements(By.XPATH, "//span[contains(text(),'尺码表')]")[0].click()

        #点击自定义尺码图
        sleep(3)
        self.assert_element("//span[contains(text(),'智能识别')]")
        # self.click("//span[contains(text(),'尺码图')]")
        # sleep(3)

        #上传尺码图
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[-1].send_keys(path + '/kwaishopuiautotest/test_data/img/test_goodAddSize.png')
        sleep(2)
        self.assert_element("//span[contains(text(),'图片解析成功')]")

        # 预览
        self.driver.find_elements(By.XPATH, "//div[@class='replace___KuPlf']")[-1].click()
        sleep(1)
        self.click("//p[contains(text(),'预览')]")
        sleep(2)
        self.driver.find_elements(By.XPATH,
                                  "//span[@class='anticon anticon-system-close-small-line goods-modal-close-icon']")[
            -1].click()
        sleep(1)


        # 裁剪框唤起
        self.driver.find_elements(By.XPATH, "//div[@class='replace___KuPlf']")[-1].click()
        self.click("//p[contains(text(),'裁剪')]")
        sleep(3)
        self.assert_text('图片裁剪', "//div[contains(text(),'图片裁剪')]")
        self.driver.find_elements(By.XPATH,"//span[@class='goods-modal-close-x']" )[-1].click()
        sleep(3)

        # 素材中心替换唤起
        self.driver.find_elements(By.XPATH, "//div[@class='replace___KuPlf']")[-1].click()
        self.click("//p[contains(text(),'素材中心替换')]")
        sleep(3)
        self.assert_text('图片素材', "//div[contains(text(),'图片素材')]")
        self.driver.find_element(By.XPATH,"//span[@class='goods-modal-close-x']" ).click()
        sleep(3)

        #删除已有图片
        self.driver.find_elements(By.XPATH,"//span[@class='anticon anticon-close-circle close-icon___QTTw1']")[3].click()
        # self.assert_text('尺码表图', "//span[contains(text(),'尺码表图')]")
        sleep(3)
        # self.assert_element('//div[contains(text(),"请输入尺码表图片")]')

    @skip('待修改')
    def test_size_manage_upload(self):

        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        self.open_url("https://s.kwaixiaodian.com/zone/goods/manage/size-charts/add?templateTypePropValueId=********")
        sleep(5)
        self.driver.find_elements(By.XPATH, "//input[@class='goods-manage-radio-input']")[1].click()
        sleep(2)

        #非1：1上传
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[0].send_keys(path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg')
        # 增加了裁剪框
        sleep(3)
        self.click("//span[contains(text(),'确 定')]")
        sleep(2)
        self.hover("//span[@class='goods-manage-upload']")
        sleep(1)
        self.assert_element("//p[contains(text(),'预览')]")

        # 删除图片
        self.click('//span[@class="anticon anticon-close-circle close-icon___yakWH"]')
        sleep(2)
        self.assert_element_not_present("//div[@class='replace___KuPlf']")

        self.refresh()
        sleep(3)
        self.driver.find_elements(By.XPATH, "//input[@class='goods-manage-radio-input']")[1].click()
        sleep(2)
        #1：1上传成功
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[0].send_keys(path + '/kwaishopuiautotest/test_data/img/white.png')
        sleep(3)
        self.hover("//span[@class='goods-manage-upload']")
        sleep(1)
        self.assert_element("//p[contains(text(),'预览')]")

        #预览
        self.click("//p[contains(text(),'预览')]")
        sleep(2)
        self.driver.find_elements(By.XPATH, "//span[@class='goods-manage-modal-close-x']")[-1].click()
        sleep(1)

    @skip('执行不稳定，先跳过')
    def test_match_shopping(self):

        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        time.sleep(4)
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")

        self.open_url("https://s.kwaixiaodian.com/zone/goods/match/purchase/add")
        sleep(6)
        self.click("//span[contains(text(),'跳过')]")
        sleep(3)

        # 上传一张非1：1图片
        path = os.path.abspath("../../..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[0].send_keys(path + '/kwaishopuiautotest/test_data/img/detail.png')
        sleep(3)
        self.assert_equal(len(self.find_elements("//div[@class='img-item']")), 0)

        # 上传一张小于828*828的图片
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[0].send_keys(path + '/kwaishopuiautotest/test_data/img/small.png')
        sleep(3)
        self.assert_equal(len(self.find_elements("//div[@class='img-item']")), 0)

        # 上传一张图片
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[0].send_keys(path + '/kwaishopuiautotest/test_data/img/match_shopping.jpg')
        sleep(3)
        self.assert_equal(len(self.find_elements("//div[@class='img-item']")), 1)

        # 删除图片，上传六张图片
        self.click('//img[@class="sortable"]')
        sleep(2)
        self.click('//span[@class="anticon anticon-system-error-circle-line delete"]')
        sleep(3)
        path1 = os.path.abspath("../../..")
        path = path1 + '/kwaishopuiautotest/test_data/img'
        file_name_list = os.listdir(path)
        path_list = [os.path.join(path, x) for x in file_name_list]
        path_list_final = [s for s in path_list if "match_shopping" in s]
        path_split_by_newline = '\n'.join(path_list_final)
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[0].send_keys(path_split_by_newline)
        sleep(10)
        # 确定有六张图片
        self.assert_equal(len(self.find_elements("//img[@class='sortable']")), 6)
        sleep(3)

        #预览图片
        sleep(3)
        self.driver.find_elements(By.XPATH, '//img[@class="sortable"]')[0].click()
        self.click('//div[contains(text(),"预览")]')
        sleep(1)
        self.click('//span[@class="kwaishop-seller-micro-goods-match-purchase-modal-close-x"]')
        sleep(3)

        # 替换，唤起素材中心弹窗
        self.driver.find_elements(By.XPATH, '//img[@class="sortable"]')[0].click()
        sleep(3)
        self.click('//span[contains(text(),"替换")]')
        sleep(1)
        self.driver.find_elements(By.XPATH, '//p[contains(text(),"素材中心导入")]')[0].click()
        sleep(3)
        self.assert_text('图片素材', "//div[contains(text(),'图片素材')]")
        self.driver.find_elements(By.XPATH, "//span[@class='kwaishop-seller-micro-goods-match-purchase-modal-close-x']")[1].click()
        sleep(3)

    #标品图片
    @pytest.mark.p1
    @skip('适配下')
    def test_standard_add(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        time.sleep(4)
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        self.driver.maximize_window()

        self.open_url("https://s.kwaixiaodian.com/zone/goods/manage/spu/list")
        sleep(6)
        self.click("//span[contains(text(),'新增标品')]")
        sleep(3)
        self.choose_category("电动车（标弱）", "标品类目", "search")
        sleep(3)
        self.click("//span[contains(text(),'下一步')]")
        sleep(3)

        self.driver.find_elements(By.XPATH, "//input[@autocomplete='off']")[1].send_keys("小熊糖")
        sleep(3)
        self.click("//span[contains(text(),'小熊糖')]")
        sleep(3)

        self.driver.find_elements(By.XPATH, "//input[@autocomplete='off']")[2].send_keys("v1")
        sleep(3)
        self.click("//div[contains(text(),'v1')]")
        sleep(3)

        ele = self.driver.find_element(By.XPATH, "//span[contains(text(),'标品主图')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(3)
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[0].send_keys(path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg')
        sleep(3)

    # 标品纠错-证明图片，标品图片
    @pytest.mark.p1
    def test_standard_error(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        time.sleep(4)
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        self.driver.maximize_window()

        item_url= UI_ITEMS_ID['standard_edit_url']
        self.open_url(item_url)
        sleep(5)

        ele = self.driver.find_element(By.XPATH, "//label[contains(text(),'证明图片')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(3)

        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[1].send_keys(path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg')
        sleep(3)

    # 评价页面
    @skip("数据过期")
    def test_evaluate(self):
        self.login("PRODUCT_DOMAIN", "product_account_2")
        self.assert_title("快手小店")

        time.sleep(10)
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        self.driver.maximize_window()
        sleep(10)

        #打开评价页面，切换至未回复页面，点开回复半屏幕

        self.open_url("https://s.kwaixiaodian.com/zone/goods/manage/comment/list")
        sleep(3)
        self.close_merchant_assistant()
        sleep(3)
        self.click("//span[contains(text(),'未回复')]")
        sleep(3)
        self.click('//*[@id="root"]/div[2]/div[2]/div[3]/div[2]/div/div/div/div/div/div/div[2]/div[1]/div[2]/div[5]/div/div/div/button/span')
        sleep(3)

        #上传图片
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[0].send_keys(path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg')
        sleep(3)
        self.click('//div[@class="goods-manage-row goods-manage-form-item goods-manage-form-item-has-success"]/div[2]/div/div/div/img')
        sleep(3)

        #预览图片
        self.click('//div[contains(text(),"预览")]')
        sleep(3)
        self.click('//span[@class="anticon anticon-system-close-medium-line goods-manage-modal-close-icon"]')
        sleep(3)

        #删除图片
        self.click('//div[@class="goods-manage-row goods-manage-form-item goods-manage-form-item-has-success"]/div[2]/div/div/div/img')
        sleep(3)
        self.click('//div[@class="goods-manage-row goods-manage-form-item goods-manage-form-item-has-success"]/div[2]/div/div/div/span')
        sleep(3)

        # 上传五张图片
        path1 = os.path.abspath("..")
        path = path1 + '/kwaishopuiautotest/test_data/img'
        file_name_list = os.listdir(path)
        path_list = [os.path.join(path, x) for x in file_name_list]
        path_list_final = [s for s in path_list if "detail" in s][1:6]
        path_split_by_newline = '\n'.join(path_list_final)
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[0].send_keys(path_split_by_newline)
        sleep(3)
        # 确定有五张图片
        self.assert_equal(len(self.find_elements("//span[@aria-label='close-circle']")), 5)
        sleep(3)

        #上传一个视频
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[1].send_keys(path + '/kwaishopuiautotest/test_data/img/video_test.mp4')
        sleep(3)
        #上传成功
        self.assert_equal(len(self.find_elements("//span[@aria-label='close-circle']")), 6)
        sleep(3)

    @skip("数据问题")
    #诊断页面，快捷设置属性
    def test_quick_prop(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        time.sleep(4)
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        self.driver.maximize_window()
        sleep(3)

        #打开诊断页面，搜索固定商品

        self.open_url("https://s.kwaixiaodian.com/zone/goods/diagnosis/list")
        sleep(3)

        self.driver.find_elements(By.XPATH, "//input[@placeholder='请输入']")[0].send_keys("**************")
        sleep(3)
        self.click("//span[contains(text(),'筛 选')]")
        sleep(3)

        #打开设置属性半屏
        self.driver.find_elements(By.XPATH, "//a[contains(text(),'设置属性')]")[0].click()
        sleep(3)

        #上传图片
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[0].send_keys(path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg')
        sleep(3)
        self.click('//div[@class="img-item"]/div/img')
        sleep(3)

        #预览图片
        self.click('//div[contains(text(),"预览")]')
        sleep(3)
        self.driver.find_elements(By.XPATH,'//span[@class="anticon anticon-system-close-medium-line diagnosis-modal-close-icon"]')[0].click()
        sleep(3)

        #删除图片
        self.click('//div[@class="img-item"]/div/img')
        sleep(3)
        self.click('//span[@class="anticon anticon-system-error-circle-line delete"]')
        sleep(3)

        #确认图片删除
        self.assert_equal(len(self.find_elements('//div[@class="img-item"]')), 0)
        sleep(3)

    @skip("代码fix")
    def test_main_photo_new(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")

        # 类目选择页
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        self.click("//span[contains(text(),'新增商品')]")
        self.driver.maximize_window()
        sleep(2)

        # 上传九张图片
        path1 = os.path.abspath("..")
        path = path1 + '/kwaishopuiautotest/test_data/img'
        file_name_list = os.listdir(path)
        path_list = [os.path.join(path, x) for x in file_name_list]
        path_list_final = [s for s in path_list if "detail" in s][1:10]
        path_split_by_newline = '\n'.join(path_list_final)
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[0].send_keys(path_split_by_newline)
        sleep(3)
        self.driver.find_elements(By.XPATH, "//span[contains(text(),'确 定')]")[0].click()
        sleep(10)
        #验证有九张
        self.assert_equal(len(self.find_elements("//span[@class='goods-upload']")),9)

        #首张裁剪，点击知道了

        self.driver.find_elements(By.XPATH, "//span[@class='goods-upload']")[1].click()
        sleep(2)
        self.driver.find_elements(By.XPATH, "//span[contains(text(),'知道了')]")[0].click()
        sleep(2)
        self.driver.find_elements(By.XPATH, "//span[@class='goods-upload']")[1].click()
        sleep(2)
        self.driver.find_elements(By.XPATH, "//p[contains(text(),'裁剪')]")[0].click()
        sleep(2)
        self.click("//span[contains(text(),'确 定')]")
        sleep(2)

        #剩余八张，每张裁剪
        for i in range(0,8):
            self.driver.find_elements(By.XPATH, "//span[@class='goods-upload']")[i+1].click()
            sleep(2)
            self.driver.find_elements(By.XPATH, "//p[contains(text(),'裁剪')]")[i+1].click()
            sleep(2)
            self.click("//span[contains(text(),'确 定')]")
            sleep(2)

        self.assert_element_not_visible("//div[contains(text(),'图片长宽比需1:1，不符合图片已红框标注，请使用裁剪工具调整')]")

    @pytest.mark.p0
    @pytest.mark.smoke
    def test_sku_photo(self):
        self.add_page("autotest-渠道校验-可售","可售类目", "search")
        #移动到部分
        elee = self.driver.find_element(By.XPATH, '//div[@id="SkuAndPrice"]')
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        sleep(1)
        self.click("//span[contains(text(),'添加规格类型(0/3)')]")
        self.click('//input[@placeholder="下拉选择或自定义输入"]')
        sleep(2)
        self.driver.find_elements(By.XPATH, '//div[@class="goods-select-item goods-select-item-option"]')[0].click()
        # sleep(3)
        # elee = self.driver.find_element(By.XPATH, "//span[contains(text(),'删除规格类型')]")
        # self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        # sleep(1)
        self.driver.find_elements(By.XPATH, '//div[@class="goods-switch-handle"]')[3].click()
        sleep(2)
        for i in range (0,6):
            self.driver.find_elements(By.XPATH, '//input[@placeholder="请输入自定义规格值"]')[i].send_keys(i)
            self.driver.find_elements(By.XPATH, "//span[contains(text(),'添加图片')]")[1].click()

        # 选择预测图片
        self.driver.find_elements(By.XPATH, '//span[@class="anticon anticon-normal-picture-line"]')[0].click()
        self.click("//div[contains(text(),'您可能会选择的图片')]/../div[2]/div/div")
        sleep(3)

        # 选择素材中心上传
        self.driver.find_elements(By.XPATH, '//span[@class="anticon anticon-normal-picture-line"]')[0].click()
        sleep(2)
        self.driver.find_elements(By.XPATH, "//p[contains(text(),'素材中心导入')]")[0].click()
        self.click("//div[contains(text(),'UI自动化专用勿动')]")
        sleep(2)
        self.driver.find_elements(By.XPATH, '//input[@class="goods-checkbox-input"]')[-1].click()
        sleep(3)
        self.click("//span[contains(text(),'确认(1)')]")
        sleep(3)

        #选择本地上传-非1：1裁剪
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[10].send_keys(path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg')
        time.sleep(3)
        self.click("//span[contains(text(),'确认裁剪')]")
        time.sleep(3)
        self.click("//span[contains(text(),'应用图片')]")
        time.sleep(1)

        # 选择本地上传-1：1不用裁剪
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[11].send_keys(path + '/kwaishopuiautotest/test_data/img/white.png')
        sleep(3)

        # 选择本地上传-jpeg
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[12].send_keys(path + '/kwaishopuiautotest/test_data/img/allow_jpeg.jpeg')
        time.sleep(5)
        self.click("//span[contains(text(),'确认裁剪')]")
        time.sleep(3)
        self.click("//span[contains(text(),'应用图片')]")
        sleep(3)

        #上传拦截
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[12].send_keys(path + '/kwaishopuiautotest/test_data/img/nobmp.bmp')
        self.assert_text('不是合法的图片类型!', "//span[contains(text(),'不是合法的图片类型!')]")

        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[12].send_keys(path + '/kwaishopuiautotest/test_data/img/nowebp.webp')
        self.assert_text('不是合法的图片类型!', "//span[contains(text(),'不是合法的图片类型!')]")

        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[12].send_keys(path + '/kwaishopuiautotest/test_data/img/big.jpg')
        self.assert_text('文件大小不能超过2MB!', "//span[contains(text(),'文件大小不能超过2MB!')]")

        #确认图片上传成功
        self.assert_equal(len(self.find_elements('//div[@class="replace___HWopB"]')), 5)


    def test_sku_photo_button(self):
        self.add_page("autotest-渠道校验-可售","可售类目", "search")
        #移动到部分
        elee = self.driver.find_element(By.XPATH, '//div[@id="SkuAndPrice"]')
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        sleep(1)
        self.click("//span[contains(text(),'添加规格类型(0/3)')]")
        self.click('//input[@placeholder="下拉选择或自定义输入"]')
        sleep(2)
        self.driver.find_elements(By.XPATH, '//div[@class="goods-select-item goods-select-item-option"]')[0].click()
        self.driver.find_elements(By.XPATH, '//input[@placeholder="请输入自定义规格值"]')[0].send_keys(0)
        self.click("//span[contains(text(),'排序')]")
        sleep(1)
        self.driver.find_elements(By.XPATH, '//div[@class="goods-switch-handle"]')[3].click()
        sleep(1)

        #选择预测图片
        self.driver.find_elements(By.XPATH, '//span[@class="anticon anticon-normal-picture-line"]')[0].click()
        sleep(1)
        self.click("//div[contains(text(),'您可能会选择的图片')]/../div[2]/div/div")
        sleep(3)

        # 预览
        self.driver.find_elements(By.XPATH, '//div[@class="replace___HWopB"]')[0].click()
        self.click("//p[contains(text(),'预览')]")
        sleep(2)
        self.driver.find_elements(By.XPATH, "//span[@class='goods-modal-close-x']")[-1].click()
        sleep(2)

        #素材中心弹窗
        self.driver.find_elements(By.XPATH, '//div[@class="replace___HWopB"]')[0].click()
        self.click("//p[contains(text(),'素材中心替换')]")
        sleep(2)
        self.assert_text('图片素材', "//div[contains(text(),'图片素材')]")
        self.driver.find_elements(By.XPATH,"//span[@class='goods-modal-close-x']" )[-1].click()
        sleep(2)

        # 裁剪框唤起
        self.driver.find_elements(By.XPATH, '//div[@class="replace___HWopB"]')[0].click()
        self.click("//p[contains(text(),'图片编辑工具')]")
        sleep(3)
        self.click("//span[contains(text(),'裁剪')]")
        time.sleep(3)
        self.click("//span[contains(text(),'确认裁剪')]")
        time.sleep(3)
        self.click("//span[contains(text(),'应用图片')]")
        sleep(3)

    @pytest.mark.p0
    # @pytest.mark.smoke # 其他case已覆盖
    def test_photoTool_cropping(self):
        """
        裁剪
        """
        self.enter_photoTool()
        self.click("//span[contains(text(),'确认裁剪')]")
        sleep(3)

        self.ensure_and_close()

    @pytest.mark.p1
    def test_photoTool_cutouts(self):
        """
        抠图
        """
        self.enter_photoTool()
        self.click("//span[contains(text(),'确认裁剪')]")
        sleep(6)
        self.click("//span[contains(text(),'抠图')]")
        sleep(1)
        self.click("//div[contains(text(),'白底图')]")
        sleep(3)
        self.click("//span[contains(text(),'立即生成')]")
        sleep(6)
        self.assert_element('//span[contains(text(),"效果图")]')

        self.click("//div[contains(text(),'透明图')]")
        self.click("//span[contains(text(),'立即生成')]")
        sleep(3)
        self.assert_element('//span[contains(text(),"效果图")]')

        self.ensure_and_close()

    @pytest.mark.p1
    def test_photoTool_restoration(self):
        """
        高清修复
        """
        self.enter_photoTool()
        self.click("//span[contains(text(),'高清修复')]")
        sleep(1)
        self.click("//div[contains(text(),'变清晰')]")
        self.click("//span[contains(text(),'立即生成')]")
        sleep(3)
        self.assert_element('//span[contains(text(),"效果图")]')

    @pytest.mark.p1
    def test_photoTool_backgroundReplacement(self):
        """
        背景替换
        """
        self.enter_photoTool()
        self.click("//span[contains(text(),'确认裁剪')]")
        sleep(3)
        self.click("//span[contains(text(),'背景替换')]")
        sleep(3)
        self.driver.find_elements(By.XPATH, '//span[@class="goods-radio"]')[0].click()
        self.click("//span[contains(text(),'立即生成')]")
        sleep(30)
        self.assert_element('//span[contains(text(),"还原原图")]')

        self.ensure_and_close()

    @pytest.mark.p1
    def test_photoTool_addSellingPoints(self):
        """
        加卖点
        """
        self.enter_photoTool()
        self.click("//span[contains(text(),'确认裁剪')]")
        sleep(3)
        self.click("//span[contains(text(),'加卖点')]")
        sleep(1)
        self.driver.find_elements(By.XPATH, '//span[@class="goods-radio"]')[0].click()
        self.click("//span[contains(text(),'立即生成')]")
        sleep(5)
        if self.is_element_visible("//button[contains(text(),'知道了')]"):
            self.logger.info("close 知道了")
            self.click("//button[contains(text(),'知道了')]")
        self.assert_element('//div[contains(text(),"文案编辑")]')

        self.ensure_and_close()

    def enter_photoTool(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")
        time.sleep(4)
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")
        self.click("//span[contains(text(),'新增商品')]")
        self.driver.maximize_window()
        time.sleep(7)
        self.close_merchant_assistant()
        # 上传主图
        path = os.path.abspath("..")
        self.choose_file('input[type="file"]', path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg')
        sleep(5)

    def ensure_and_close(self):
        # 试5次，每次3s，最多15s,裁剪过程时慢时快
        re_time = 0
        while (self.is_element_visible("//div[contains(text(),'图片编辑工具')]") and re_time < 5):
            sleep(3)
            if self.is_element_visible("//span[contains(text(),'应用图片')]"):
                self.click("//span[contains(text(),'应用图片')]")
            re_time += 1
        # 预览图片
        self.click("//div[@class='replace___KuPlf']")
        sleep(1)
        self.driver.find_elements(By.XPATH, '//div[@class="material-icon"]')[0].click()
        self.assert_element('//span[contains(text(),"预览")]')



































