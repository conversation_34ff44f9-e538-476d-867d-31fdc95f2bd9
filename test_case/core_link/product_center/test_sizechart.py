import pytest
from time import sleep
from test_case.core_link.product_center.base import BaseTestCase
import os
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from unittest import skip

class size_chart(BaseTestCase):


    @pytest.mark.p1
    def test_sizemanage_link(self):
        self.add_page("autotest-渠道校验-可售","可售类目", "search")
        elee = self.driver.find_element(By.XPATH, "//span[contains(text(),'商品规格')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)

        sleep(3)
        self.driver.find_elements(By.XPATH, "//span[contains(text(),'尺码表')]")[0].click()
        sleep(3)
        self.click("//a[contains(text(),'管理尺码表模版')]")
        sleep(3)
        url = self.get_current_url()
        self.assert_link_status_code_is_not_404(url)

        self.switch_to_window(1)
        handles = self.driver.window_handles
        self.driver.switch_to.window(handles[-1])
        self.click("//span[contains(text(),'新增尺码模版')]")
        sleep(3)
        url_2 = self.get_current_url()
        self.assert_link_status_code_is_not_404(url_2)

    @pytest.mark.p1
    @skip('待修改')
    def test_add_sizeInfoInput(self):
        self.add_page("autotest-渠道校验-可售","可售类目", "search")
        elee = self.driver.find_element(By.XPATH, "//span[contains(text(),'商品规格')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)

        # 填写，多行，上下移，删除，注释
        self.driver.find_element(By.XPATH, "//input[@id='sizeList_0_尺码_0']").send_keys('L')
        self.driver.find_element(By.XPATH, "//input[@id='sizeList_0_身高_0']").send_keys('160')
        self.driver.find_element(By.XPATH, "//input[@id='sizeList_0_身高_1']").send_keys('170')
        self.driver.find_element(By.XPATH, "//input[@id='sizeList_0_体重_0']").send_keys('60')
        self.driver.find_element(By.XPATH, "//input[@id='sizeList_0_体重_1']").send_keys('70')
        # self.driver.find_element(By.XPATH, "//input[@id='sizeList_0_胸围_0']").send_keys('160')
        # self.driver.find_element(By.XPATH, "//input[@id='sizeList_0_胸围_1']").send_keys('170')

        self.click('//span[contains(text(),"+添加行")]')
        sleep(1)
        self.driver.find_element(By.XPATH, "//input[@id='sizeList_1_尺码_0']").send_keys('XL')
        self.driver.find_element(By.XPATH, "//input[@id='sizeList_1_身高_0']").send_keys('160')
        self.driver.find_element(By.XPATH, "//input[@id='sizeList_1_身高_1']").send_keys('170')
        self.driver.find_element(By.XPATH, "//input[@id='sizeList_1_体重_0']").send_keys('60')
        self.driver.find_element(By.XPATH, "//input[@id='sizeList_1_体重_1']").send_keys('70')
        # self.driver.find_element(By.XPATH, "//input[@id='sizeList_1_胸围_0']").send_keys('160')
        # self.driver.find_element(By.XPATH, "//input[@id='sizeList_1_胸围_1']").send_keys('170')

        self.driver.find_elements(By.XPATH, "//span[contains(text(),'上移')]")[-1].click()
        self.driver.find_elements(By.XPATH, "//span[contains(text(),'下移')]")[0].click()
        self.driver.find_elements(By.XPATH, "//span[contains(text(),'删除')]")[-1].click()

        self.driver.find_element(By.XPATH, "//input[@placeholder='请填写标题注释，如：测量方式相关']").send_keys('测试文案')

        # 预览图片
        sleep(5)
        self.click('//span[contains(text(),"预览图片")]')
        sleep(3)
        self.assert_element('//span[contains(text(),"下载图片")]')
        self.click("//div[@class='goods-modal goods-modal-confirm goods-modal-confirm-info']//span[@aria-label='system-close-small-line']//*[name()='svg']")

        # 另存为模版 弹窗
        self.click('//span[contains(text(),"另存为模板")]')
        sleep(3)
        self.assert_element_not_present('//span[contains(text(),"请完善尺码表后另存为模板")]')
        self.driver.find_element(By.XPATH, "//input[@placeholder='请输入模版名称']").send_keys(
            '测试文案')
        self.driver.find_elements(By.XPATH, "//span[@class='goods-select-selection-search']")[-1].click()
        self.click('//div[contains(text(),"中国码")]')
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"取 消")]')[-1].click()

        # 适用尺码图
        self.click('//span[contains(text(),"使用尺码图")]')
        sleep(1)
        self.assert_element('//div[contains(text(),"尺码表图")]')
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[-1].send_keys(
            path + '/kwaishopuiautotest/test_data/img/test_addMainImage2.jpeg')
        sleep(3)
        self.assert_element_not_present('//div[contains(text(),"尺码表图")]')

