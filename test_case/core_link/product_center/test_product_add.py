#!/usr/bin/env/ python
# coding:utf-8
"""
 Author : zongxunqiang
 Time : 2022-07-20 20:37
 DESC :
 
 """
import pytest
from time import sleep
from test_case.core_link.product_center.base import BaseTestCase
import os
from selenium.webdriver.common.by import By
from unittest import skip
from selenium.webdriver.common.keys import Keys


class TestSytCustomer(BaseTestCase):

    @pytest.mark.p0
    @pytest.mark.smoke
    def test_add_product(self):
        self.add_page("autotest-渠道校验-可售","可售类目", "search")

        # 校验页面元素
        self.assert_no_404_errors()
        #必填项填写
        #填写标题
        self.type("//input[@placeholder=\"最多输入30个汉字（60个字符）\"]", "UI自动化新增商品")
        sleep(1)
        # self.driver.find_elements(By.XPATH, '//span[contains(text(),"否")]')[0].click()
        #填写详情图
        ele = self.driver.find_element(By.XPATH, '//p[contains(text(),"商品图文")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(1)
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[-2].send_keys(
            path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg')
        sleep(5)
        self.assert_text('继续上传', "//span[contains(text(),'继续上传')]")
        # 添加3:4主图 3张
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[2].send_keys(
            path + '/kwaishopuiautotest/test_data/img/test_threeFourImage.png')
        sleep(5)
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[3].send_keys(
            path + '/kwaishopuiautotest/test_data/img/test_threeFourImage.png')
        sleep(5)
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[4].send_keys(
            path + '/kwaishopuiautotest/test_data/img/test_threeFourImage.png')
        sleep(5)

        #停用尺码表
        ele = self.driver.find_element(By.XPATH, '//p[contains(text(),"价格库存")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(1)
        self.click('//span[contains(text(),"停用尺码表")]')
        sleep(2)
        self.click('//span[contains(text(),"停 用")]')
        sleep(2)
        if self.is_element_visible("//span[contains(text(),'知道了')]"):
            self.click("//span[contains(text(),'知道了')]")

        # #填写价格库存
        for i in range(2):
            self.driver.find_elements(By.XPATH,"//div[@class='goods-input-number-input-wrap']/input")[i].send_keys("1")
        self.driver.find_elements(By.XPATH, "//input[@placeholder='请输入']")[-1].send_keys('111111111')

        self.submit_add_product()

        self.assert_element("//p[contains(text(),'商品上传成功')]")
        self.click('//span[contains(text(),"返回列表")]')
        sleep(5)
        self.assert_title("快手小店")

    def submit_add_product(self):
        # 提交审核
        ele = self.driver.find_element(By.XPATH, '//span[contains(text(),"设置多件减钱或打折的优惠")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(1)
        # self.click('//span[contains(text(),"提交审核")]')
        # sleep(4)
        # if self.is_element_visible('//span[contains(text(),"继续提交")]'):
        #     self.click('//span[contains(text(),"继续提交")]')
        #     sleep(5)
        # 发品可能因测试账号失败，兼容以下场景
        max_num = 5
        while self.is_element_visible('//span[contains(text(),"提交审核")]'):
            if self.is_element_visible("//div[@role='alert'][contains(text(),'请正确填写运费模板')]"):
                text = '默认全国（非港澳台）包邮模板'
                self.click('//*[@id="itemRelease_expressTemplateId"]/div[1]/div/div[1]/span[1]')
                sleep(3)
                self.click("//span[contains(text(),'" + text + "')]")
                sleep(3)
            self.click('//span[contains(text(),"提交审核")]')
            sleep(4)
            if self.is_element_visible('//span[contains(text(),"继续提交")]'):
                self.click('//span[contains(text(),"继续提交")]')
                sleep(5)
            max_num -= 1
            if max_num < 0:
                break

    @pytest.mark.p0
    @pytest.mark.smoke
    def test_add_product_with_decoration_details(self):
        self.add_page("autotest-渠道校验-可售","可售类目", "search")

        # 校验页面元素
        self.assert_no_404_errors()
        #必填项填写
        #填写标题
        self.type("//input[@placeholder=\"最多输入30个汉字（60个字符）\"]", "UI自动化新增商品带装修商详信息")
        sleep(1)
        # self.driver.find_elements(By.XPATH, '//span[contains(text(),"否")]')[0].click()
        #填写详情图
        ele = self.driver.find_element(By.XPATH, '//p[contains(text(),"商品图文")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(1)
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[-2].send_keys(
            path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg')
        sleep(5)
        self.assert_text('继续上传', "//span[contains(text(),'继续上传')]")
        # 添加3:4主图 3张
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[2].send_keys(
            path + '/kwaishopuiautotest/test_data/img/test_threeFourImage.png')
        sleep(5)
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[3].send_keys(
            path + '/kwaishopuiautotest/test_data/img/test_threeFourImage.png')
        sleep(5)
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[4].send_keys(
            path + '/kwaishopuiautotest/test_data/img/test_threeFourImage.png')
        sleep(5)

        # 增加，装修商详
        ele = self.driver.find_element(By.XPATH, '//span[contains(text(),"白底图必须白背景、透明图必须为透明背景")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(1)
        self.assert_element('//div[contains(text(),"装修商详")]')
        self.click('//span[contains(text(),"装修商详")]')
        sleep(2)
        self.assert_element('//div[contains(text(),"装修组件")]')
        self.assert_element('//div[contains(text(),"基础组件")]')
        self.assert_element('//div[contains(text(),"高级组件")]')
        self.assert_element('//div[contains(text(),"布局设置")]')
        if self.is_element_visible("//span[contains(text(),'知道了')]"):
            self.click("//span[contains(text(),'知道了')]")

        self.driver.find_elements(By.XPATH, '//span[contains(text(),"保 存")]')[-1].click()
        sleep(3)
        if self.is_element_visible("//span[contains(text(),'知道了')]"):
            self.click("//span[contains(text(),'知道了')]")
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"保 存")]')[0].click()
        sleep(3)

        #停用尺码表
        ele = self.driver.find_element(By.XPATH, '//p[contains(text(),"价格库存")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(1)
        self.click('//span[contains(text(),"停用尺码表")]')
        sleep(2)
        self.click('//span[contains(text(),"停 用")]')
        sleep(2)
        if self.is_element_visible("//span[contains(text(),'知道了')]"):
            self.click("//span[contains(text(),'知道了')]")

        # #填写价格库存
        for i in range(2):
            self.driver.find_elements(By.XPATH,"//div[@class='goods-input-number-input-wrap']/input")[i].send_keys("1")
        self.driver.find_elements(By.XPATH, "//input[@placeholder='请输入']")[-1].send_keys('111111111')

        self.submit_add_product()

        self.assert_element("//p[contains(text(),'商品上传成功')]")
        self.click('//span[contains(text(),"返回列表")]')
        sleep(5)
        self.assert_title("快手小店")

    @pytest.mark.p1
    def test_add_product_50sku(self):
        self.add_page("测试类目新增", "多预制项类目", "search")

        # 校验页面元素
        self.assert_no_404_errors()

        sku = self.driver.find_element(By.XPATH, '//span[contains(text(),"商品规格")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", sku)
        sleep(1)

        self.driver.find_elements(By.XPATH, '//input[@class="goods-checkbox-input"]')[0].click()
        sleep(1)

        sku_56 = self.driver.find_element(By.XPATH, '//div[contains(text(),"PRT预制56")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", sku_56)
        sleep(1)

        self.assert_text('单个规格最多支持50个规格值', "//div[contains(text(),'单个规格最多支持50个规格值')]")

    @skip("类目属性变更，待重新配置放开")
    @pytest.mark.p1
    def test_add_product_multisku(self):
        self.add_page("测试类目新增", "多预制项类目", "search")

        # 校验页面元素
        self.assert_no_404_errors()
        #填写标题
        self.type("//input[@placeholder=\"最多输入30个汉字（60个字符）\"]", "UI自动化新增商品-多sku商品")
        sleep(2)

        #填写属性
        ele = self.driver.find_element(By.XPATH, '//label[@for="itemRelease_goodsAttrs"]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        self.driver.find_elements(By.XPATH,'//div[@class="goods-select goods-select-single goods-select-allow-clear goods-select-show-arrow goods-select-show-search"]')[0].click()
        sleep(3)
        self.driver.find_elements(By.XPATH, '//div[@class="rc-virtual-list-holder-inner"]/div[2]')[-1].click()
        sleep(3)
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"确 定")]')[-1].click()

        # ele = self.driver.find_element(By.XPATH, "//span[contains(text(),'单选呀')]")
        # self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        # sleep(1)
        #
        # self.driver.find_elements(By.XPATH,'//div[@class="goods-select goods-select-single goods-select-allow-clear goods-select-show-arrow goods-select-show-search"]')[2].click()
        # sleep(3)
        # self.driver.find_elements(By.XPATH, '//div[@class="rc-virtual-list-holder-inner"]/div[2]')[-1].click()
        # sleep(3)
        # self.driver.find_elements(By.XPATH, "//span[contains(text(),'确 定')]")[1].click()
        # sleep(3)


        # 填写详情图
        elee = self.driver.find_element(By.XPATH, '//div[@id="itemRelease_detailImageUrls"]')
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        sleep(5)
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[3].send_keys(
            path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg')
        sleep(5)
        self.assert_text('继续上传', "//span[contains(text(),'继续上传')]")

        sku_48 = self.driver.find_element(By.XPATH, '//div[contains(text(),"PRT预制48")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", sku_48)
        sleep(1)

        #填写自定义规格
        for i in range(0, 4):
            self.driver.find_elements(By.XPATH, '//input[@placeholder="请输入自定义规格值"]')[0].send_keys(i)
            self.click("//div[contains(text(),'PRT预制56')]")

        #填写价格库存

        stock = self.driver.find_element(By.XPATH, '//span[contains(text(),"价格库存")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", stock)

        self.driver.find_elements(By.XPATH, '//input[@placeholder="库存（件）"]')[0].send_keys(100)
        self.driver.find_elements(By.XPATH, '//input[@placeholder="价格（元）"]')[0].send_keys(100)
        self.click('//span[contains(text(),"批量设置")]')

        #设置有效期
        date = self.driver.find_element(By.XPATH, '//span[contains(text(),"设置有效期")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", date)
        sleep(2)
        self.driver.find_elements(By.XPATH, '//input[@class="goods-radio-input"]')[7].click()
        self.assert_text('有效期天数1-360天', "//div[contains(text(),'有效期天数1-360天')]")
        sleep(1)
        self.driver.find_element(By.XPATH,'//span[contains(text(),"天内有效")]/../div/div[2]/input').send_keys(10)

        # 提交审核
        self.click('//span[contains(text(),"提交审核")]')
        sleep(8)
        self.assert_text('商品上传成功', "//p[contains(text(),'商品上传成功')]")
        self.click('//span[contains(text(),"返回列表")]')
        sleep(5)
        self.assert_title("快手小店")

    @skip("组件更新，修改后放开")
    @pytest.mark.p1
    def test_edit_product_100sku(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")
        sleep(5)

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")

        # 输入固定商品id
        self.type("//input[@placeholder=\"多条之间用单个逗号分隔(不要有空格)，最多50个\"]", "**************")
        self.click('//span[contains(text(),"查 询")]')
        sleep(1)
        # 普通编辑
        self.click('//span[contains(text(),"发布相似品")]/../../../span[1]/button/span')  # 编辑和普通编辑在一个组件中，通过发布同款搜寻
        sleep(10)

        # 停用尺码表
        size = self.driver.find_element(By.XPATH, '//label[contains(text(),"尺码注释")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", size)
        self.click('//span[contains(text(),"停用尺码表")]')
        sleep(2)
        self.click('//span[contains(text(),"停 用")]')
        sleep(2)

        # 填写价格库存

        stock = self.driver.find_element(By.XPATH, '//span[contains(text(),"现货+预售")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", stock)
        sleep(1)

        #筛选款式为1
        self.driver.find_elements(By.XPATH, '//div[@class="PQDlvaRC1tZWwC8DzZyA')[-1].click()
        sleep(1)
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"全部")]')[2].click()
        sleep(1)
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"111")]')[0].click()
        sleep(1)
        self.driver.find_elements(By.XPATH, '//input[@placeholder="库存（件）"]')[0].send_keys(1)
        self.driver.find_elements(By.XPATH, '//input[@placeholder="价格（元）"]')[0].send_keys(99)
        self.click('//span[contains(text(),"批量设置")]')
        sleep(2)

        self.click('//span[contains(text(),"提交审核")]')
        # 普通编辑成功页
        self.assert_text('商品编辑成功', '//*[text()="商品编辑成功"]')
        self.assert_text('返回商品列表', '//*[text()="返回商品列表"]')
        # 普通编辑成功页返回商品列表
        self.click('//span[contains(text(),"返回商品列表")]')
        self.assert_text('首页', '//span[contains(text(),"首页")]')

        # 输入固定商品id
        sleep(5)
        self.type("//input[@placeholder=\"多条之间用单个逗号分隔(不要有空格)，最多50个\"]", "**************")
        self.click('//span[contains(text(),"查 询")]')
        sleep(5)

        #库存校验
        page = self.driver.find_element(By.XPATH, '//span[contains(text(),"10 条/页")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", page)
        sleep(1)

        self.assert_text('100','//div[@class="HypnPl8WyKe1PZYkRwit"]/div')
        self.assert_text('99', '//div[@class="MiTEZ40YdjuI8QXkiOZ0"]/div')
        self.assert_text('100', '//div[@class="MiTEZ40YdjuI8QXkiOZ0"]/div[3]')
        sleep(5)

        # 免审编辑
        self.click('//span[contains(text(),"发布相似品")]/../../../span[2]/button/span')  # 编辑和普通编辑在一个组件中，通过发布同款搜寻
        sleep(10)
        # 填写价格库存

        stock = self.driver.find_element(By.XPATH, '//span[contains(text(),"全部现货")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", stock)

        # 筛选款式为1
        self.click('//span[contains(text(),"款式")]/../../div[2]')
        sleep(1)
        self.click('//div[@class="rc-virtual-list-holder-inner"]/div[2]')
        sleep(1)

        self.driver.find_elements(By.XPATH, '//input[@placeholder="库存（件）"]')[0].send_keys(0)
        self.driver.find_elements(By.XPATH, '//input[@placeholder="价格（元）"]')[0].send_keys(100)
        self.click('//span[contains(text(),"批量设置")]')
        sleep(2)

        self.click('//span[contains(text(),"提交审核")]')
        # 免审编辑成功页
        self.assert_text('商品信息更新成功', '//*[text()="商品信息更新成功"]')
        self.assert_text('返回商品列表', '//*[text()="返回商品列表"]')
        # 免审编辑成功页返回商品列表
        self.click('//span[contains(text(),"返回商品列表")]')
        self.assert_text('首页', '//span[contains(text(),"首页")]')

        # 输入固定商品id
        sleep(5)
        self.type("//input[@placeholder=\"多条之间用单个逗号分隔(不要有空格)，最多50个\"]", "**************")
        self.click('//span[contains(text(),"查 询")]')
        sleep(5)

        # 库存校验
        page = self.driver.find_element(By.XPATH, '//span[contains(text(),"10 条/页")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", page)
        sleep(1)

        self.assert_text('0', '//div[@class="HypnPl8WyKe1PZYkRwit"]/div')
        self.assert_text('100', '//div[@class="MiTEZ40YdjuI8QXkiOZ0"]/div')

    @pytest.mark.p1
    def test_add_product_price(self):
        self.add_page("autotest-渠道校验-可售", "可售类目", "search")

        # 校验页面元素
        self.assert_no_404_errors()
        # 填写价格
        price = self.driver.find_element(By.XPATH, '//span[contains(text(),"价格库存")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", price)

        self.driver.find_elements(By.XPATH, '//input[@placeholder="最少0.01"]')[0].send_keys(100)
        sleep(1)

        # 划线价填写-大于单价
        self.driver.find_elements(By.XPATH, '//input[@placeholder="需要大于单价"]')[0].send_keys(99)
        self.assert_text('划线价必须大于当前单价', "//div[contains(text(),'划线价必须大于当前单价')]")
        sleep(2)
        self.double_click('//input[@placeholder="需要大于单价"]')
        sleep(1)
        # 划线价-不超过10倍
        self.driver.find_elements(By.XPATH, '//input[@placeholder="需要大于单价"]')[0].send_keys(101)
        self.assert_not_equal('划线价必须大于当前单价', "//div[contains(text(),'划线价必须大于当前单价')]")
        sleep(1)
        self.double_click('//input[@placeholder="需要大于单价"]')
        sleep(1)
        #划线价-不超过10倍
        self.driver.find_elements(By.XPATH, '//input[@placeholder="需要大于单价"]')[0].send_keys(10000)
        self.assert_text('划线价不可超过单价的10倍', "//div[contains(text(),'划线价不可超过单价的10倍')]")
        sleep(1)
        self.double_click('//input[@placeholder="需要大于单价"]')
        sleep(1)
        # 划线价-不超过10倍
        self.driver.find_elements(By.XPATH, '//input[@placeholder="需要大于单价"]')[0].send_keys(101)
        self.assert_not_equal('划线价不可超过单价的10倍', "//div[contains(text(),'划线价不可超过单价的10倍')]")
        sleep(1)

    @pytest.mark.p2
    @skip('带修改')
    def test_stop_sizeChart(self):
        """
        停用尺码表 停用尺码表后，尺码表组件不展示
        """
        self.add_page("autotest-渠道校验-可售","可售类目", "search")

        size = self.driver.find_element(By.XPATH, '//label[contains(text(),"尺码注释")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", size)
        self.click('//span[contains(text(),"停用尺码表")]')
        sleep(2)
        self.click('//span[contains(text(),"停 用")]')
        sleep(2)
        self.assert_element_not_visible('//label[contains(text(),"尺码注释")]', by="css selector", timeout=None)
        self.assert_element('//span[contains(text(),"启用尺码表")]')

    @skip("删除不生效，再看下")
    def test_add_product_remark(self):
        self.login("PRODUCT_DOMAIN", "product_account")
        self.assert_title("快手小店")
        sleep(5)

        # 展示商品列表
        self.click("//span[contains(text(),'商品')]")
        self.click("//span[contains(text(),'商品列表')]")

        # 输入固定商品id
        self.type("//input[@placeholder=\"多条之间用单个逗号分隔(不要有空格)，最多50个\"]", "**************")
        self.click('//span[contains(text(),"查 询")]')
        sleep(1)
        # 普通编辑
        self.click('//span[contains(text(),"发布相似品")]/../../../span[1]/button/span')  # 编辑和普通编辑在一个组件中，通过发布同款搜寻
        sleep(10)

        #填写备注
        remark = self.driver.find_element(By.XPATH, '//span[contains(text(),"商品备注")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", remark)
        self.type("//input[@placeholder=\"支持中文、英文和数字，最多10个汉字(20个字符）\"]", "新增一个备注")

        # 停用尺码表
        size = self.driver.find_element(By.XPATH, '//label[contains(text(),"尺码注释")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", size)
        self.click('//span[contains(text(),"停用尺码表")]')
        sleep(2)
        self.click('//span[contains(text(),"停 用")]')
        sleep(2)

        #提交审核
        self.click('//span[contains(text(),"提交审核")]')
        sleep(3)
        self.click('//span[contains(text(),"继续发布")]')
        sleep(8)
        # 普通编辑成功页
        self.assert_text('商品编辑成功', '//*[text()="商品编辑成功"]')
        self.assert_text('返回商品列表', '//*[text()="返回商品列表"]')
        # 普通编辑成功页返回商品列表
        self.click('//span[contains(text(),"返回商品列表")]')
        self.assert_text('首页', '//span[contains(text(),"首页")]')

        #搜索展示备注
        self.type("//input[@placeholder=\"多条之间用单个逗号分隔(不要有空格)，最多50个\"]", "**************")
        self.click('//span[contains(text(),"查 询")]')
        sleep(3)
        self.assert_text('商品备注: 新增一个备注', '//div[contains(text(),"商品备注: 新增一个备注")]')
        sleep(3)

        # 普通编辑
        self.click('//span[contains(text(),"发布相似品")]/../../../span[1]/button/span')  # 编辑和普通编辑在一个组件中，通过发布同款搜寻
        sleep(10)

        # 删除备注
        remark = self.driver.find_element(By.XPATH, '//span[contains(text(),"商品备注")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", remark)
        self.double_click('//input[contains(text(),"新增一个备注")]')
        self.find_elements(By.XPATH, '//input[contains(text(),"新增一个备注")]')[0].send_keys("Keys.BACK_SPACE")
        # 停用尺码表
        size = self.driver.find_element(By.XPATH, '//label[contains(text(),"尺码注释")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", size)
        self.click('//span[contains(text(),"停用尺码表")]')
        sleep(2)
        self.click('//span[contains(text(),"停 用")]')
        sleep(2)
        # 提交审核
        self.click('//span[contains(text(),"提交审核")]')
        # 普通编辑成功页
        self.assert_text('商品编辑成功', '//*[text()="商品编辑成功"]')
        self.assert_text('返回商品列表', '//*[text()="返回商品列表"]')
        # 普通编辑成功页返回商品列表
        self.click('//span[contains(text(),"返回商品列表")]')
        self.assert_text('首页', '//span[contains(text(),"首页")]')

        # 搜索不展示备注
        self.type("//input[@placeholder=\"多条之间用单个逗号分隔(不要有空格)，最多50个\"]", "**************")
        self.click('//span[contains(text(),"查 询")]')
        sleep(3)
        self.assert_not_equal('商品备注: 新增一个备注', '//div[contains(text(),"商品备注: 新增一个备注")]')
        sleep(3)

    @skip("多买多折功能下线 20241010")
    def test_multi_discount(self):
        self.add_page("autotest-销售属性支持单位", "多买多折类目", "search")
        elee = self.driver.find_element(By.XPATH, '//span[contains(text(),"商品规格")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        sleep(3)
        #打开多买多折弹窗，验证已开启
        self.click("//button[@class='goods-switch goods-switch-small']")
        self.assert_text('消费者浏览效果', '//h3[contains(text(),"消费者浏览效果")]')
        #输入最小规格及单位
        self.type("//input[@placeholder=\"数量\"]", "1")
        sleep(2)
        self.assert_text('请选择单位', '//div[contains(text(),"请选择单位")]')
        self.driver.find_elements(By.XPATH, "//input[@placeholder=\"数量\"]/../div")[-1].click()
        sleep(2)
        self.driver.find_elements(By.XPATH, "//div[@class='goods-select-item-option-content']")[-1].click()
        sleep(3)
        #输入规格
        self.driver.find_elements(By.XPATH, "//span[@class='goods-input-affix-wrapper']/input")[-2].send_keys(10)
        self.driver.find_elements(By.XPATH, "//span[@class='goods-input-affix-wrapper']/input")[-1].send_keys(9)
        sleep(3)
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"添加规格")]')[-1].click()
        self.driver.find_elements(By.XPATH, "//span[@class='goods-input-affix-wrapper']/input")[-2].send_keys(8)
        self.driver.find_elements(By.XPATH, "//span[@class='goods-input-affix-wrapper']/input")[-1].send_keys(7)
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"确 定")]')[-1].click()
        self.click('//span[contains(text(),"确 认")]')
        sleep(2)
        #规格回填
        self.assert_element('//input[@value=\"打9折\"]')
        self.assert_element('//input[@value=\"打7折\"]')
        #批量设置价格隐藏
        elee = self.driver.find_element(By.XPATH, '//div[contains(text(),"全部")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        self.assert_not_equal('价格','//input[contains(text(),"价格")]')
        #输入价格，计算其余价格
        self.type("//input[@placeholder=\"最少0.01\"]", "10")
        sleep(2)
        #跳转回多买多折，打开编辑多买多折弹窗
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"(多买多折")]/span')[0].click()
        sleep(2)
        self.click('//span[contains(text(),"前往调整")]')
        sleep(2)
        #关闭多买多折，批量价格恢复
        self.driver.find_elements(By.XPATH, "//button[@class='goods-switch goods-switch-small goods-switch-checked']")[0].click()
        sleep(2)
        elee = self.driver.find_element(By.XPATH, '//div[contains(text(),"货品模式")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        sleep(2)
        self.assert_element("//input[@placeholder=\"价格\"]")

    @pytest.mark.p2
    def test_decoration_details(self):
        """
        装修商详
        """
        self.add_page("autotest-渠道校验-可售","可售类目", "search", account="product_account_2")

        #移动到部分
        elee = self.driver.find_element(By.XPATH, "//span[contains(text(),'商品主体需清晰完整、并与实物保持一致')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        sleep(5)
        path = os.path.abspath("..")
        self.driver.find_elements(By.XPATH, '//input[@type="file"]')[-2].send_keys(path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg')
        sleep(3)
        self.assert_text('继续上传', "//span[contains(text(),'继续上传')]")

        self.assert_element('//div[contains(text(),"装修商详")]')
        self.click('//span[contains(text(),"装修商详")]')
        sleep(2)
        self.assert_element('//div[contains(text(),"装修组件")]')
        self.assert_element('//div[contains(text(),"基础组件")]')
        self.assert_element('//div[contains(text(),"高级组件")]')
        self.assert_element('//div[contains(text(),"布局设置")]')

        self.driver.find_elements(By.XPATH, '//span[contains(text(),"保 存")]')[-1].click()
        sleep(1)
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"保 存")]')[0].click()
        sleep(3)

        self.assert_text('继续上传', "//span[contains(text(),'继续上传')]")

    @pytest.mark.p1
    def test_multiple_offers(self):
        """
        多件优惠组件
        """
        self.add_page("autotest-渠道校验-可售", type="search")

        # 移动到部分
        elee = self.driver.find_element(By.XPATH, "//div[contains(text(),'可销售库存由在sku上维护的库存决定')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        sleep(3)

        # 检查文案和链接跳转
        self.assert_element('//span[contains(text(),"设置多件减钱或打折的优惠，吸引用户下单并提升客单价")]')
        self.click('//a[contains(text(),"查看教程")]')
        sleep(3)
        self.switch_to_window(1)
        self.assert_no_404_errors()

        # 输入框-正常
        self.switch_to_window(0)
        self.driver.find_element(By.XPATH, "//input[@placeholder='2-99']").send_keys('3')
        sleep(1)
        self.assert_element_not_present('//div[contains(text(),"请输入2到99之间的整数")]')
        # 清空输入框
        self.driver.execute_script("arguments[0].value = '';",
                                   self.driver.find_element(By.XPATH, "//input[@placeholder='2-99']"))
        self.driver.find_element(By.XPATH, "//input[@placeholder='2-99']").send_keys('333')
        sleep(1)
        self.assert_element('//div[contains(text(),"请输入2到99之间的整数")]')

        self.driver.find_element(By.XPATH, "//input[@placeholder='1-9.9']").send_keys('8.8')
        sleep(1)
        self.assert_element_not_present('//div[contains(text(),"请输入1到9.9之间的数字")]')
        self.driver.execute_script("arguments[0].value = '';",
                                   self.driver.find_element(By.XPATH, "//input[@placeholder='1-9.9']"))
        self.driver.find_element(By.XPATH, "//input[@placeholder='1-9.9']").send_keys('10')
        sleep(1)
        self.assert_element('//div[contains(text(),"请输入1到9.9之间的数字")]')

    @pytest.mark.p2
    def test_free_shipping_diable(self):
        """
        顺丰包邮服务组件 未开通
        """
        self.add_page("autotest-渠道校验-可售", type="search")

        # 移动到部分
        elee = self.driver.find_element(By.XPATH, "//span[contains(text(),'运费模板中的发货地址和运费将在商品详情页展示')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        sleep(1)

        # 检查文案和链接跳转
        self.assert_element('//span[contains(text(),"可为商品勾选开通下述物流增值服务，提升商品竞争力")]')
        self.assert_element('//span[contains(text(),"暂未开通服务，开通后将享受专属卖点，流量加权等权益")]')
        self.click('//span[contains(text(),"去开通")]')
        sleep(3)
        self.switch_to_window(1)
        self.assert_element('//section[contains(text(),"为店铺内商品开通顺丰包邮服务后，消费者逛-买全链路均可见「顺丰包邮」提示，可提升商品转化，获得更多流量曝光")]')

        # 切回页面有一个提示窗
        self.switch_to_window(0)
        self.assert_element('//span[contains(text(),"确定已开通顺丰包邮服务？")]')
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"确 定")]')[-1].click()
        self.assert_element_not_present('//span[contains(text(),"确定已开通顺丰包邮服务？")]')

    @pytest.mark.p2
    def test_free_shipping_enable(self):
        """
        顺丰包邮服务组件 已开通
        """
        self.add_page("autotest-渠道校验-可售", type="search", account="product_account_2")

        # 移动到部分
        elee = self.driver.find_element(By.XPATH, "//span[contains(text(),'可为商品勾选开通下述物流增值服务')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        sleep(1)

        # 检查文案和链接跳转
        self.assert_element('//span[contains(text(),"可为商品勾选开通下述物流增值服务，提升商品竞争力")]')
        self.assert_element('//span[contains(text(),"商品开通服务后，商家需以顺丰快递履约，消费者逛-买全链路均可见「顺丰包邮」提示，商品可获得更多流量曝光")]')
        self.click('//a[contains(text(),"查看详情")]')
        sleep(3)
        self.switch_to_window(1)
        self.assert_element('//section[contains(text(),"为店铺内商品开通顺丰包邮服务后，消费者逛-买全链路均可见「顺丰包邮」提示，可提升商品转化，获得更多流量曝光")]')

        # 切回页面-勾选后选择模版
        self.switch_to_window(0)
        elee = self.driver.find_element(By.XPATH, "//span[contains(text(),'运费模板中的发货地址和运费将在商品详情页展示')]")
        self.driver.execute_script("arguments[0].scrollIntoView();", elee)
        sleep(1)

        self.driver.find_elements(By.XPATH, "//input[@class='goods-checkbox-input']")[-1].click()
        sleep(3)
        self.assert_element('//span[contains(text(),"请创建并设置服务模板，否则无法享受顺丰包邮权益")]')
        self.driver.find_elements(By.XPATH, "//input[@autocomplete='off']")[-5].send_keys('test')
        sleep(2)
        self.click("//div[contains(text(),'test服务模版1')]")
        sleep(2)
        self.click('//span[contains(text(),"查看和设置模板信息")]')
        sleep(3)
        self.switch_to_window(-1)
        self.assert_no_404_errors()

        self.switch_to_window(0)
        self.click('//span[contains(text(),"创建模板")]')
        sleep(3)
        self.switch_to_window(-1)
        self.assert_no_404_errors()

        self.switch_to_window(0)
        sleep(1)
        self.assert_element('//span[contains(text(),"确定已完成模板创建？")]')
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"确 定")]')[-1].click()

        # 取消勾选
        self.driver.find_elements(By.XPATH, "//input[@class='goods-checkbox-input']")[-1].click()
        sleep(3)
        self.assert_element('//span[contains(text(),"确定取消该商品的顺丰包邮服务？")]')
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"确定取消")]')[-1].click()
        sleep(1)
        self.assert_element_not_present('//span[contains(text(),"请创建并设置服务模板，否则无法享受顺丰包邮权益")]')

    @pytest.mark.p1
    def test_presale_change(self):
        self.add_page("其他钟表","钟表类目","search")
        # 限购示例
        ele = self.driver.find_element(By.XPATH, '//label[@for="itemRelease_spec"]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(3)

        # 全部现货
        self.assert_element('//span[contains(text(),"全部现货")]')
        self.assert_element('//span[contains(text(),"现货承诺发货时间")]')
        self.assert_element('//span[contains(text(),"24小时")]')
        self.assert_element('//span[contains(text(),"48小时")]')
        self.click("div[class='block___cDckP'] div label[class='goods-radio-wrapper']")
        sleep(1)

        # 现货➕预售
        self.click('//span[contains(text(),"现货+预售")]')
        sleep(1)
        if self.is_element_visible('//div[contains(text(),"若有现货库存，建议使用【现货】模式，提高下单转化率，减少用户退款")]'):
            sleep(1)
            self.click('//span[contains(text(),"继续设置现货+预售")]')
            sleep(3)
        self.assert_element(
            '//span[contains(text(),"请勿虚设预售时效，并谨慎配置30天及以上的预售时效，以免对店铺分/商品卡流量/转化率等产生影响，平台建议合理配置发货时效，优先使用现货模式。")]')
        self.assert_element('//span[contains(text(),"现货承诺发货时间")]')
        self.assert_element('//span[contains(text(),"预售发货时间")]')

        self.click('//span[contains(text(),"+ 添加发货时效")]')
        sleep(1)
        self.assert_element('//div[contains(text(),"预售发货时间不能相同")]')
        text_path = '//*[@id="itemRelease_serviceRule_deliveryTimeMode"]/div/div/div[3]/div/div/div[3]/span[2]/div[2]/div/div[2]/input'
        self.type(text_path, "4")
        sleep(1)
        self.assert_element_not_present('//div[contains(text(),"预售发货时间不能相同")]')
        self.driver.find_elements(By.XPATH, '//span[contains(text(),"删除")]')[0].click()
        sleep(1)

        # 全部预售
        self.click('//span[contains(text(),"全部预售")]')
        sleep(1)
        if self.is_element_visible('//div[contains(text(),"若有现货库存，建议使用【现货】模式，提高下单转化率，减少用户退款")]'):
            sleep(1)
            self.click('//span[contains(text(),"继续设置预售")]')
            sleep(3)
        self.assert_element(
            '//span[contains(text(),"请勿虚设预售时效，并谨慎配置30天及以上的预售时效，以免对店铺分/商品卡流量/转化率等产生影响，平台建议合理配置发货时效，优先使用现货模式。")]')
        self.assert_element('//span[contains(text(),"预售发货时间")]')
        text_path = '//*[@id="itemRelease_serviceRule_deliveryTimeMode"]/div/div/div[4]/div/div/div[2]/span[2]/div/div/div[2]/input'
        self.type(text_path, "1")
        self.assert_element('//div[contains(text(),"预售发货时间需要在3-15天之间")]')
        sleep(1)
        self.type(text_path, "4")
        self.assert_element_not_present('//div[contains(text(),"预售发货时间需要在3-15天之间")]')

    @pytest.mark.p1
    def test_brand_declare(self):
        self.add_page("autotest-渠道校验-可售","可售类目", "search")

        # 发品页 跳转品牌申报页面
        ele = self.driver.find_element(By.XPATH, '//div[contains(text(),"属性完成度")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(1)

        self.driver.find_elements(By.XPATH, "//span[@class='goods-select-selection-search']")[0].click()
        sleep(3)
        self.click('//span[contains(text(),"申报关联类目")]')
        sleep(3)
        self.switch_to_window(-1)

        # 跳转查看审核记录页面
        self.assert_element('//span[contains(text(),"申报结果会通过「卖家端首页-消息-店铺动态」告知，请您关注。")]')
        self.click('//span[contains(text(),"查看审核记录")]')
        sleep(5)
        self.switch_to_window(-1)
        self.assert_element('//div[contains(text(),"品牌申报查询")]')
        self.assert_element('//span[contains(text(),"通过")]')

        # 文本框、下拉选择框
        self.switch_to_window(-2)
        self.type("//input[@placeholder='请输入品牌中文名称']", "测试申报中文品牌")
        self.type("//input[@placeholder='请输入品牌英文名称']", "testBrand")
        self.assert_element_not_present('//div[contains(text(),"中文/英文名请至少填写一个")]')
        self.assert_element_not_present('//div[contains(text(),"品牌中文名格式不正确。")]')
        self.assert_element_not_present('//div[contains(text(),"品牌英文名格式不正确。")]')

        self.driver.find_elements(By.XPATH, "//span[@class='ant-select-selection-search']")[0].click()
        sleep(2)
        self.click("//div[contains(text(),'中国')]")

        self.type("//input[@placeholder='请输入商标注册号/申请号']", "41461553")
        self.assert_element_not_present('//div[contains(text(),"请输入商标注册号/申请号")]')

        # 图片上传
        path = os.path.abspath("..")
        self.choose_file('input[type="file"]', path + '/kwaishopuiautotest/test_data/img/service_market_image.jpg')
        sleep(3)

        # 类目选择器
        self.click("//span[contains(text(),'新增类目')]")
        sleep(3)
        self.assert_element('//div[contains(text(),"类目选择")]')
        self.driver.find_elements(By.XPATH, "//input[@autocomplete='off']")[-1].send_keys("autotest-渠道校验-可售")
        sleep(3)
        self.click("//div[contains(text(),'autotest-渠道校验-可售')]")
        sleep(3)
        self.click("//span[contains(text(),'确 定')]")
        sleep(3)
        self.assert_element('//span[contains(text(),"测试书籍测试类目 > 自动化测试类目 > autotest-渠道校验-可售")]')

    def test_add_productSpecifications(self):
        self.add_page("autotest-渠道校验-可售","可售类目", "search")

        # 发品页 到商品规格处
        ele = self.driver.find_element(By.XPATH, '//span[contains(text(),"建议您填写尺码表")]')
        self.driver.execute_script("arguments[0].scrollIntoView();", ele)
        sleep(1)

        # 添加属性规格

        #




