import pytest
from ddt import ddt
from test_case.growth.kuaishouxiaodian.base import BaseTestCase
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class TestJingYingJianYi(BaseTestCase):
    def checkout_moudle(self):
        self.login("MERCHANT_HOME_DOMAIN", "zhengguihua")
        self.driver.maximize_window()


    def check_element_non_existence(self, element):
        #检验元素不存在
        li = []
        try:
            e = WebDriverWait(self.driver, 10).until(EC.invisibility_of_element_located((By.ID, element)))
            li.append(e)
            if len(li) > 0:
                return True
            else:
                return False
        except TimeoutException:
            "弹窗没关掉！！！"

    def test_jingyingjianyi(self):
        self.checkout_moudle()
        self.sleep(5)
        self.assert_text("经营建议","//span[text()='经营建议']")
        self.assert_text("攻略", "//div[text()='攻略']")
        self.assert_text("查看更多", "//span[text()='查看更多']")

        # 关闭弹窗
        self.click("#driver-popover-item > div.driver-clearfix.driver-popover-footer > button")
        self.sleep(2)

        # 缩小经营助手
        self.click("#kpro-tool-box--sellerHelperBox > div.middleBoxTitle___Q8qIR > img")
        self.sleep(2)

        #点击 查看更多
        self.click("#kpro-seller-v2-diagnose-suggestion > div > div.header___NgttE > div.action_wrap___wfOn8")
        self.sleep(2)

        self.assert_text("经营建议","//span[text()='经营建议']")
        self.assert_text("攻略", "//div[text()='攻略']")
        self.assert_element('html > div > div > div.ant-drawer-content-wrapper > div > div > div.ant-drawer-body > div > div')
        self.assert_element('html > div > div > div.ant-drawer-content-wrapper > div > div > div.ant-drawer-body > div > div > div.kpro-seller-v2-diagnose-suggestion--container-drawer-pagination > ul')
        self.sleep(2)
        self.click('html > div > div > div.ant-drawer-content-wrapper > div > div > div.ant-drawer-header > div > button')
        self.sleep(2)
        self.assert_true(self.check_element_non_existence('html > div > div > div.ant-drawer-content-wrapper > div > div > div.ant-drawer-body'))

        # 还原经营助手
        self.click("#kpro-tool-box--sellerHelperBox > span")
        self.sleep(2)