from time import sleep

from seleniumbase import BaseCase

# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain
# 获取账号信息
from constant.account import get_account_info
from constant.domain import get_domain, get_domain_by_env
# 导入元素高亮工具
from utils.element_highlighter import ElementHighlighter


class BaseTestCase(BaseCase):

    def __init__(self, *args, **kwargs):
        """初始化BaseTestCase，创建元素高亮工具实例"""
        super().__init__(*args, **kwargs)
        self._highlighter = None

    @property
    def highlighter(self):
        """获取元素高亮工具实例"""
        if self._highlighter is None:
            self._highlighter = ElementHighlighter(self.driver)
        return self._highlighter

        def login(self, domain, account):
            account_data = get_account_info(account)
            # 用户名 account_data['account']
            # 密码 account_data['password']
            host = get_domain(domain)

            self.open(host)
            self.sleep(2)

            if self.is_element_visible("(//span[contains(text(),'登 录')])[1]") or self.is_element_visible(
                    "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
                self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]")
                self.type("input[placeholder='请输入手机号']", account_data['account'])
                self.sleep(1)
                self.type("input[placeholder='请输入密码']", account_data['password'])
                self.click("//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div")
                self.sleep(2)
            else:
                self.click("(//span[contains(text(),'密码登录')])[1]")
                self.sleep(1)
                self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data['account'])
                self.sleep(1)
                self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
                self.sleep(1)
                self.click(
                    "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
                self.sleep(2)

        def login2(self, domain, accountNo, pwd):
            # account_data = get_account_info(account)
            # 用户名 account_data['account']
            # 密码 account_data['password']
            host = get_domain(domain)

            self.open(host)
            self.sleep(3)
            # self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]/span')
            # self.click('//*[@id="username"]')
            # self.type('//*[@id="username"]', accountNo)
            # self.click('//*[@id="password"]')
            # self.type('//*[@id="password"]', pwd)
            # self.click('//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div[1]/div[3]/form/div[3]/div/div/div/button/span')
            self.click("(//span[contains(text(),'密码登录')])[1]")
            self.sleep(1)
            self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", accountNo)
            self.sleep(1)
            self.type("(//input[@placeholder='请输入密码'])[1]", pwd)
            self.sleep(1)
            self.click("(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
            self.sleep(2)
        # 天河测试平台使用勿动
        def login3(self, host, accountNo, pwd):
            # "https://sso.corp.kuaishou.com/cas/login"
            self.open(host)
            self.sleep(1)
            self.click("//div[@id='loginTabSso']")
            self.click('//input[@id=\'ssoUsernameInput\']')
            self.type('//input[@id=\'ssoUsernameInput\']', accountNo)
            self.click('//input[@id=\'ssoPasswordInput\']')
            self.type('//input[@id=\'ssoPasswordInput\']', pwd)
            self.click('//button[@id=\'ssoSubmit\']')

        def login4(self, host, accountNo, pwd):
            #https://apsso.corp.kuaishou.com/apsso
            self.open(host)
            self.sleep(2)
            # 点击账号密码
            self.click("//div[@id='loginTabSso']")
            self.click("//input[@id='ssoUsernameInput']")
            self.type("//input[@id='ssoUsernameInput']", accountNo)
            self.click("//input[@id='ssoPasswordInput']")
            self.type("//input[@id='ssoPasswordInput']", pwd)
            self.click("//label[@id='ssoSubmitLabel']")

        def loginAI(self, url):
            if "cps" in url:
                account_data = get_account_info("wb_caijinwei")

                self.open(url)
                self.sleep(1)

                if self.is_element_visible("(//span[contains(text(),'密码登录')])[1]") or self.is_element_visible(
                        "//*[@id=\"root\"]/div/div[2]/div/div[1]/div/div/div[2]/div[2]/div[2]/div[1]"):
                    self.click("(//span[contains(text(),'密码登录')])[1]")
                    self.sleep(1)
                    self.type("(//input[@placeholder='请输入手机号/邮箱'])[1]", account_data['account'])
                    self.sleep(1)
                    self.type("(//input[@placeholder='请输入密码'])[1]", account_data['password'])
                    self.sleep(1)
                    self.click(
                        "(//button[@class='text-[16px] flex text-[white] font-[600] pt-[12px] pb-[12px] w-full hover:opacity-80 items-center justify-center gap-[4px] leading-[24px] currentC svelte-am7f5d'])[1]")
                    self.sleep(2)
                else:
                    self.click('#root>div>div:nth-child(2)>div>div>div>div:nth-child(2)>div:nth-child(1)')
                    # self.assert_text("扫码登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(1)")  # div标签中的第一个元素
                    # self.assert_text("手机号登录", "div.dvUB6Z08ZFpU4wHcKxSv > div:nth-child(2)")
                    self.sleep(2)
                    self.click("//div[@class='choseTab___okqX0']//div[2]")
                    self.type("input[placeholder='请输入手机号']", account_data['account'])
                    self.sleep(1)
                    self.type("input[placeholder='请输入密码']", account_data['password'])
                    self.click("button[type='button']")
                    self.sleep(5)
                self.sleep(3)
                self.refresh()

        # 天河测试平台使用勿动
        def login3(self, host, accountNo, pwd):
            # "https://sso.corp.kuaishou.com/cas/login"
            self.open(host)
            self.sleep(0.5)
            self.click('//*[@id="loginTabSso"]')
            self.click('//*[@id="ssoUsernameInput"]')
            self.type('//*[@id="ssoUsernameInput"]', accountNo)
            self.click('//*[@id="ssoPasswordInput"]')
            self.type('//*[@id="ssoPasswordInput"]', pwd)
            self.click('//*[@id="ssoSubmit"]')

    # ==================== 元素高亮相关方法 ====================

    def highlight_element_by_xpath(self, xpath, style_type="success", label_text=None, timeout=None, duration=0):
        """
        通过xpath查找并高亮元素

        Args:
            xpath: xpath表达式
            style_type: 高亮样式类型 (success, warning, error, info, primary)
            label_text: 标签文本
            timeout: 查找超时时间
            duration: 高亮持续时间（秒），0表示不自动移除

        Returns:
            dict: 操作结果
        """
        return self.highlighter.highlight_by_xpath(xpath, style_type, label_text, timeout, duration)

    def highlight_element(self, element, style_type="success", label_text=None, duration=0):
        """
        高亮显示已找到的元素

        Args:
            element: WebElement对象
            style_type: 高亮样式类型
            label_text: 标签文本
            duration: 高亮持续时间（秒）

        Returns:
            bool: 是否成功高亮
        """
        return self.highlighter.highlight_element(element, style_type, label_text, duration)

    def find_element_by_xpath_with_highlight(self, xpath, style_type="success", label_text=None, timeout=None):
        """
        查找元素并自动高亮

        Args:
            xpath: xpath表达式
            style_type: 高亮样式类型
            label_text: 标签文本
            timeout: 查找超时时间

        Returns:
            WebElement对象或None
        """
        element = self.highlighter.find_element_by_xpath(xpath, timeout)
        if element:
            self.highlighter.highlight_element(element, style_type, label_text)
        return element

    def click_with_highlight(self, xpath, style_type="primary", label_text="点击元素", highlight_duration=1):
        """
        高亮元素后点击

        Args:
            xpath: xpath表达式
            style_type: 高亮样式类型
            label_text: 标签文本
            highlight_duration: 高亮持续时间（秒）
        """
        # 先高亮元素
        result = self.highlight_element_by_xpath(xpath, style_type, label_text, duration=highlight_duration)

        if result["success"]:
            # 等待高亮效果
            if highlight_duration > 0:
                self.sleep(highlight_duration)
            # 点击元素
            self.click(xpath)
            return True
        else:
            raise Exception(f"无法找到要点击的元素: {xpath}")

    def type_with_highlight(self, xpath, text, style_type="info", label_text="输入文本", highlight_duration=1):
        """
        高亮元素后输入文本

        Args:
            xpath: xpath表达式
            text: 要输入的文本
            style_type: 高亮样式类型
            label_text: 标签文本
            highlight_duration: 高亮持续时间（秒）
        """
        # 先高亮元素
        result = self.highlight_element_by_xpath(xpath, style_type, f"{label_text}: {text}", duration=highlight_duration)

        if result["success"]:
            # 等待高亮效果
            if highlight_duration > 0:
                self.sleep(highlight_duration)
            # 输入文本
            self.type(xpath, text)
            return True
        else:
            raise Exception(f"无法找到要输入的元素: {xpath}")

    def highlight_multiple_elements(self, xpath_list, style_type="success", auto_label=True):
        """
        批量高亮多个元素

        Args:
            xpath_list: xpath表达式列表
            style_type: 高亮样式类型
            auto_label: 是否自动添加标签

        Returns:
            dict: 批量操作结果
        """
        return self.highlighter.highlight_multiple_elements(xpath_list, style_type, auto_label)

    def clear_all_highlights(self):
        """清除所有高亮"""
        self.highlighter.clear_all_highlights()

    def assert_element_visible_with_highlight(self, xpath, style_type="success", label_text="验证元素可见"):
        """
        验证元素可见并高亮显示

        Args:
            xpath: xpath表达式
            style_type: 高亮样式类型
            label_text: 标签文本
        """
        # 先验证元素可见
        self.assert_element_visible(xpath)

        # 然后高亮元素
        result = self.highlight_element_by_xpath(xpath, style_type, label_text)

        if not result["success"]:
            raise Exception(f"元素可见但高亮失败: {xpath}")

    def wait_and_highlight_element(self, xpath, timeout=10, style_type="success", label_text="等待元素出现"):
        """
        等待元素出现并高亮

        Args:
            xpath: xpath表达式
            timeout: 等待超时时间
            style_type: 高亮样式类型
            label_text: 标签文本

        Returns:
            WebElement对象或None
        """
        # 等待元素可见
        self.wait_for_element_visible(xpath, timeout=timeout)

        # 高亮元素
        result = self.highlight_element_by_xpath(xpath, style_type, label_text)

        return result.get("element") if result["success"] else None
