# -*- coding: utf-8 -*-
"""
@Time ： 2025/2/17 11:40 AM
@Auth ： zhanle
@File ：test_new_trade.py.py
@IDE ：PyCharm
"""
import random
from unittest import skip

from ddt import ddt
from test_case.merchant_syt.base import BaseTestCase
from test_case.merchant_syt.home.syt_trade_page import TradePage
from utils.http_help import BaseHttpRequest
import pytest
import re
account_proxy_app = BaseHttpRequest(user_id="B_**********")


class TestSytLiveNew(BaseTestCase):

    def checkout_trade_old(self):

        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        self.refresh()
        #新手引导
        if self.is_element_visible("//div[@class='syt-main-modal-content']"):
            self.click("//span[contains(text(),'跳过')]")
            self.sleep(1)
        #大促弹窗
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(3)
        self.sleep(1)
        self.click("(//span[contains(text(),'交易')])[1]")
        self.sleep(10)



    def checkout_trade_new(self):

        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        self.open("https://syt.kwaixiaodian.com/zones/tradeManagement/dealAnalysis")
        self.sleep(10)

    def checkout_refund_new(self):

        self.login("SYT_DOMAIN", "supply_account")
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, **********)
        self.open("https://syt.kwaixiaodian.com/zones/tradeManagement/refundAnalysis")
        self.sleep(10)

    #全部商品
    @pytest.mark.p0
    def test_trade_overView(self):
        self.checkout_trade_old()

        real_info1 = self.get_text(
            "(//div[@class='kwaishop-tianhe-tradeManagement-pc-pro-checkableTags-wrapper'])[1]")
        self.assert_in("全部", real_info1)
        self.assert_in("自营", real_info1)
        self.assert_in("达人合作", real_info1)
        self.assert_in("超级链接", real_info1)
        real_info2 = self.get_text(
            "(//div[@class='kwaishop-tianhe-tradeManagement-pc-pro-checkableTags-wrapper'])[2]")
        self.assert_in("全部", real_info2)
        self.assert_in("直播间", real_info2)
        self.assert_in("商品卡", real_info2)
        self.assert_in("短视频", real_info2)

    #核心指标
    @pytest.mark.p0
    def test_trade_core_overView(self):
        self.checkout_trade_new()

        real_info1 = self.get_text(
            "//div[contains(@class,'Lu_uagW5KraNOPLN4n8d')]")
        self.assert_in("成交金额", real_info1)
        self.assert_in("退款金额（支付日）", real_info1)
        self.assert_in("退款金额（退款日）", real_info1)
        self.assert_in("预估佣金", real_info1)
        self.assert_in("结算佣金", real_info1)


    #核心指标
    @pytest.mark.p0
    def test_trade_core_overView_live(self):
        self.checkout_trade_new()
        self.click("(//span[contains(text(),'直播间')])[1]")
        self.sleep(3)
        real_info1 = self.get_text(
            "//div[contains(@class,'Lu_uagW5KraNOPLN4n8d')]")
        self.assert_in("成交金额", real_info1)
        self.assert_in("退款金额（支付日）", real_info1)
        self.assert_in("退款金额（退款日）", real_info1)
        self.assert_not_in("预估佣金", real_info1)
        self.assert_not_in("结算佣金", real_info1)

    #载体构成-售卖方式拆分
    @pytest.mark.p0
    def test_trade_compose_1(self):
        self.checkout_trade_new()
        real_info1 = self.get_text("//div[@class='kpro-data-calculation-normal-cards']")
        self.assert_in("整体成交", real_info1)
        self.assert_in("自营成交", real_info1)
        self.assert_in("达人合作成交", real_info1)
        self.assert_in("超级链接", real_info1)

        real_info2 = self.get_text("//div[@class='kpro-data-calculation-simple-cards']")
        self.assert_in("直播间", real_info2)
        self.assert_in("短视频", real_info2)
        self.assert_in("商品卡", real_info2)
        self.assert_in("其他", real_info2)

        xpathStr = '/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div[2]/div[2]/div[2]/div[1]/div/div/div/div[{idx}]'
        idx = str(random.choice([num for num in range(3, 5, 7)]))
        path = xpathStr.format(idx=idx)
        self.click(path)
        self.sleep(2)
        real_info2 = self.get_text("//div[@class='kpro-data-calculation-simple-cards']")
        self.assert_in("直播间", real_info2)
        self.assert_in("短视频", real_info2)
        self.assert_in("商品卡", real_info2)
        self.assert_in("其他", real_info2)
        self.assert_element("//div[@id='sytWholeDealCarrier']//div//div[contains(@customstyle,'[object Object]')]//div[contains(@customstyle,'[object Object]')]//div//div//div//canvas")

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_trade_compose_live_desc(self):
        self.checkout_trade_new()
        self.click("(//span[contains(text(),'详情')])[1]")
        # 成交订单数降序
        self.click(
            "(//span[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align')][contains(text(),'成交订单数')])[2]")
        self.sleep(1)
        self.click(
            "(//span[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align')][contains(text(),'成交订单数')])[2]")
        self.sleep(2)
        meynumber1 = self.find_element(TradePage.trade_detail_l1).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element( TradePage.trade_detail_l2).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_trade_compose_live_reverse(self):
        self.checkout_trade_new()
        self.click("(//span[contains(text(),'详情')])[1]")

        text = self.find_element(TradePage.trade_detail_num).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:
            self.sleep(1)
            title1 = self.find_element(TradePage.trade_detail_zhibojiang_title).text
            self.click(TradePage.trade_detail_next)
            self.sleep(3)
            title2 = self.find_element(TradePage.trade_detail_zhibojiang_title).text
            self.sleep(3)

            assert title1 != title2

    @pytest.mark.p0
    def test_trade_compose_photo_desc(self):
        self.checkout_trade_new()
        self.click("(//span[contains(text(),'详情')])[2]")
        # 成交订单数降序
        self.click(
            "(//span[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align')][contains(text(),'成交订单数')])[2]")
        self.sleep(1)
        self.click(
            "(//span[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align')][contains(text(),'成交订单数')])[2]")
        self.sleep(2)
        meynumber1 = self.find_element(TradePage.trade_detail_l1).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(TradePage.trade_detail_l2).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 >= mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_trade_compose_photo_reverse(self):
        self.checkout_trade_new()
        self.click("(//span[contains(text(),'详情')])[2]")

        text = self.find_element(TradePage.trade_detail_num).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:

            title1 = self.find_element(TradePage.trade_detail_photo_title).text

            self.click(TradePage.trade_detail_next)
            self.sleep(3)
            title2 = self.find_element(TradePage.trade_detail_photo_title).text
            self.sleep(3)
            substr1 = title1[:11]
            substr2 = title2[:11]
            assert len(substr1)>0  and len(substr2)>0

    @pytest.mark.p0
    def test_trade_compose_goods_desc(self):
        self.checkout_trade_new()
        self.click("(//span[contains(text(),'详情')])[3]")
        # 成交订单数降序
        self.click(
            "(//span[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align')][contains(text(),'成交订单数')])[2]")
        self.sleep(1)
        self.click(
            "(//span[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align')][contains(text(),'成交订单数')])[2]")
        self.sleep(2)
        meynumber1 = self.find_element(TradePage.trade_detail_card_l1).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(TradePage.trade_detail_card_l2).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_trade_compose_goods_reverse(self):
        self.checkout_trade_new()
        self.click("(//span[contains(text(),'详情')])[3]")

        text = self.find_element(TradePage.trade_detail_num).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:

            title1 = self.find_element(TradePage.trade_detail_card_title).text

            self.click("(//li[contains(@title,'下一页')])[2]")
            self.sleep(3)
            title2 = self.find_element(TradePage.trade_detail_card_title).text
            self.sleep(3)
            substr1 = title1[:11]
            substr2 = title2[:11]
            assert len(substr1)>0  and len(substr2)>0

    #载体构成-售卖载体拆分
    @pytest.mark.p0
    def test_trade_compose_2(self):
        self.checkout_trade_new()
       # self.click("//span[contains(text(),'按售卖载体拆分')]")

        # xpathStr = '/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div[2]/div[3]/div[2]/div[1]/div/div/div/div[{idx}]'
        #
        #
        #
        # idx = str(random.choice([num for num in range(3,5,7)]))
        # path = xpathStr.format(idx=idx)
        self.click( '/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div[2]/div[3]/div[2]/div[1]/div/div/div/div[1]')
        self.sleep(2)

        real_info2 = self.get_text('/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div[2]/div[3]/div[2]/div[2]/div[1]/div[3]/div')
        real_info3 = self.get_text('/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div[2]/div[3]/div[2]/div[2]/div[1]/div[5]/div')
       # self.assert_in("自营", real_info2)
        self.assert_in("达人合作", real_info2)
        self.assert_in("超级链接", real_info3)
        self.assert_element("//div[@id='sytWholeDealCarrier']//div//div[contains(@customstyle,'[object Object]')]//div[contains(@customstyle,'[object Object]')]//div//div//div//canvas")

    @pytest.mark.p0
    def test_trade_compose_self_desc(self):
        self.checkout_trade_new()
        self.click( '/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div[2]/div[3]/div[2]/div[1]/div/div/div/div[1]')
        self.click('/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div[2]/div[3]/div[2]/div[2]/div[1]/div[1]/div/div[1]/div[1]/div[1]/span')
        # 成交订单数降序
        self.click(
            "(//span[@class='kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align'][contains(text(),'成交订单数')])[2]")
        self.sleep(1)
        self.click(
            "(//span[@class='kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align'][contains(text(),'成交订单数')])[2]")
        self.sleep(2)
        meynumber1 = self.find_element(TradePage.trade_detail_l1).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(TradePage.trade_detail_l2).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_trade_compose_self_reverse(self):
        self.checkout_trade_new()
        self.click(TradePage.trade_zaiti_card)
        self.click('/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div[2]/div[3]/div[2]/div[2]/div[1]/div[1]/div/div[1]/div[1]/div[1]/span')

        text = self.find_element(TradePage.trade_detail_num).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:

            title1 = self.find_element(TradePage.trade_detail_zhibojiang_title).text


            self.click("(//li[contains(@title,'下一页')])[2]")
            self.sleep(3)
            title2 = self.find_element(TradePage.trade_detail_zhibojiang_title).text
            self.sleep(3)
            substr1 = title1[:11]
            substr2 = title2[:11]
            assert len(substr1)>0 and len(substr2)>0
    #@pytest.mark.skip
    @pytest.mark.p0
    def test_trade_compose_cooperate_desc(self):
        self.checkout_trade_new()
        self.click(TradePage.trade_zaiti_card)
        self.click('/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div[2]/div[3]/div[2]/div[2]/div[1]/div[1]/div/div[1]/div[1]/div[1]/span')
        self.sleep(2)
        # 成交订单数降序
        self.click(
            "(//span[@class='kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align'][contains(text(),'成交订单数')])[2]")
        self.sleep(2)
        meynumber1 = self.find_element(
            TradePage.trade_detail_l1).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
           TradePage.trade_detail_l2).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 >= mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_trade_compose_cooperate_reverse(self):
        self.checkout_trade_new()
        self.click(TradePage.trade_zaiti_card)
        self.click('/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div[2]/div[3]/div[2]/div[2]/div[1]/div[1]/div/div[1]/div[1]/div[1]/span')


        text = self.find_element(
            TradePage.trade_detail_num).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:
            title1 = self.find_element(TradePage.trade_detail_zhibojiang_title).text

            self.click("(//li[contains(@title,'下一页')])[2]")
            self.sleep(3)
            title2 = self.find_element( TradePage.trade_detail_zhibojiang_title).text
            self.sleep(3)
            substr1 = title1[:11]
            substr2 = title2[:11]
            assert  len(substr1) >0 and len(substr2)>0

    #账号构成
    @pytest.mark.p0
    def test_trade_account_compose(self):
        self.checkout_trade_new()

        xpathStr1 = '/html/body/div/div[1]/div/section/section/main/div[2]/div/div/div/div[2]/div[4]/div[2]/div[1]/div/div[1]/div/div/div[{idx}]'
        idx1 = str(random.choice([num for num in range(1, 5)]))
        path1 = xpathStr1.format(idx=idx1)
        self.click(path1)
        xpathStr2 ='//*[@id="dilu_micro_root"]/div/div[2]/div[4]/div[2]/div[1]/div/div[2]/div/div/div[2]/div/div/span[1]'
        # idx2 = str(random.choice([num for num in range(1, 5)]))
        # path2 = xpathStr2.format(idx=idx2)
        self.click(xpathStr2)
        self.sleep(3)

        real_info1 = self.get_text('//*[@id="dilu_micro_root"]/div/div[2]/div[4]/div[3]/div/div/div/div/div/div/div/div/div/div/div/div/table/thead')
        self.assert_in("成交金额（元）", real_info1)
        self.assert_in("退款金额（支付日）", real_info1)
        self.assert_in("成交订单数", real_info1)
        self.assert_in("成交人数", real_info1)
        self.assert_in("近7天成交金额趋势", real_info1)
        self.assert_in("成交人数", real_info1)
        self.assert_in("客单价", real_info1)
        self.assert_in("退款金额(退款日)", real_info1)

    @pytest.mark.p0
    def test_trade_account_compose_desc(self):
        self.checkout_trade_new()
        # xpathStr1 = '//*[@id="root"]/div/div/div[4]/div[1]/div[1]/div/div[1]/div/div/div[{idx}]'
        # idx1 = str(random.choice([num for num in range(1, 5)]))
        # path1 = xpathStr1.format(idx=idx1)
        self.click(TradePage.trade_account_zhibo)
        # xpathStr2 = '//*[@id="dilu_micro_root"]/div/div[2]/div[4]/div[2]/div[1]/div/div[2]/div/div/div[{idx2}]'
        # idx2 = str(random.choice([num for num in range(1, 5)]))
        # path2 = xpathStr2.format(idx=idx2)
        # self.click(path2)
        self.sleep(3)

        # 成交订单数降序
        self.click(
            "(//span[@class='kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align'][contains(text(),'成交订单数')])[1]")
        self.sleep(1)
        self.click(
            "(//span[@class='kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align'][contains(text(),'成交订单数')])[1]")
        self.sleep(2)
        meynumber1 = self.find_element(
            TradePage.trade_account_l1).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            TradePage.trade_account_l2).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2


    @pytest.mark.p0
    def test_trade_account_compose_reverse(self):
        self.checkout_trade_new()
        # xpathStr1 = '//*[@id="dilu_micro_root"]/div/div[4]/div[1]/div[1]/div/div[1]/div/div/div[{idx}]'
        # idx1 = str(random.choice([num for num in range(1, 5)]))
        # path1 = xpathStr1.format(idx=idx1)
        # self.click(path1)
        # xpathStr2 = '//*[@id="dilu_micro_root"]/div/div[4]/div[1]/div[1]/div/div[2]/div/div/div[{idx}]'
        # idx2 = str(random.choice([num for num in range(1, 5)]))
        # path2 = xpathStr2.format(idx=idx2)
        self.click(TradePage.trade_account_zhibo)
        self.sleep(3)
        text = self.find_element(TradePage.trade_account_num).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:
            title1 = self.find_element(
            TradePage.trade_account_title).text

            self.click(TradePage.trade_account_next)
            self.sleep(3)
            title2 = self.find_element(
           TradePage.trade_account_title).text
            self.sleep(3)
            substr1 = title1[:8]
            substr2 = title2[:8]
            assert substr1 != substr2


    #全部-实时
    @pytest.mark.p0
    def test_trade_core_overView_real(self):
        self.checkout_trade_new()
        self.click(TradePage.trade_date_real)
        self.sleep(3)
        real_info1 = self.get_text(
            "//div[contains(@class,'Lu_uagW5KraNOPLN4n8d')]")
        self.assert_in("成交金额", real_info1)
        self.assert_in("退款金额（支付日）", real_info1)
        self.assert_in("退款金额（退款日）", real_info1)
        self.assert_in("预估佣金", real_info1)
        self.assert_in("成交订单数", real_info1)
        self.assert_in("成交人数", real_info1)

    @pytest.mark.p0
    def test_trade_compose_real_desc(self):
        self.checkout_trade_new()
        self.click(TradePage.trade_date_real)
        self.sleep(3)

        # xpathStr = '/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[{idx}]/div[1]'
        # idx = str(random.choice([num for num in range(3, 6, 2)]))
        # path = xpathStr.format(idx=idx)
        self.click('//*[@id="sytWholeDealCarrier"]/div[2]/div[2]/div[1]/div[1]/div/div[1]/div[1]/div[1]/span')
        self.sleep(2)

       # // self.click("(//span[contains(text(),'详情')])[3]")
        # 成交订单数降序
        self.click(
            "(//span[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align')][contains(text(),'成交订单数')])[2]")
        self.sleep(1)
        self.click(
            "(//span[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align')][contains(text(),'成交订单数')])[2]")
        self.sleep(2)
        meynumber1 = self.find_element(
            TradePage.trade_detail_l1).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            TradePage.trade_detail_l2).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    # 载体构成-售卖载体拆分-载体二级页
    @pytest.mark.p0
    def test_trade_compose_real_reverse(self):
        self.checkout_trade_new()
        self.click(
            TradePage.trade_date_real)
        self.sleep(3)

        # xpathStr = '/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[{idx}]/div[1]'
        # idx = str(random.choice([num for num in range(3, 6, 2)]))
        # path = xpathStr.format(idx=idx)
        ## 实时短视频成交
        self.click('/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div[2]/div[3]/div[2]/div[1]/div/div/div/div[5]/div')
        self.sleep(2)
        self.click('/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div[2]/div[3]/div[2]/div[2]/div[1]/div[1]/div/div[1]/div[1]/div[1]/span')

        text = self.find_element(
            TradePage.trade_detail_num).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:
            title1 = self.find_element(
            TradePage.trade_detail_photo_id).text


            self.click(TradePage.trade_detail_next)
            self.sleep(3)
            title2 = self.find_element(
                TradePage.trade_detail_photo_id
            ).text
            self.sleep(3)
            substr1 = title1[:11]
            substr2 = title2[:11]
            assert substr1 != substr2

    @pytest.mark.p0
    def test_trade_compose_real_desc_tfc(self):
        self.checkout_trade_new()

        self.click(
            TradePage.trade_date_real)
        self.sleep(3)
        # self.click("//span[contains(text(),'按售卖载体拆分')]")
        #
        # xpathStr = '/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[5]/div[1]'
        # idx = str(random.choice([num for num in range(3, 8, 2)]))
        # path = xpathStr.format(idx=idx)
        # self.click(path)
        self.click(
            '/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div[2]/div[3]/div[2]/div[1]/div/div/div/div[5]/div')
        self.sleep(2)
        self.click(
            '/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div[2]/div[3]/div[2]/div[2]/div[1]/div[1]/div/div[1]/div[1]/div[1]/span')
        self.sleep(2)

        number = self.find_element(
            '//*[@id="sytWholeDealCarrier"]/div[2]/div[2]/div[1]/div[3]/div/div[1]/div[2]/div/span[1]').text
        match = number.replace(',', '')
        num1 = int(float(match))
        print(num1)

        if num1 == 0:
            return

            text = self.find_element('//*[@id="dilu_micro_root"]/div/div[5]/div/div/div[2]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/ul/li[1]').text
            match = re.search(r'共 (\d+) 条', text)
            num = int(match.group(1))
            if num > 2:
            # 成交订单数降序
                self.click("(//span[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align')][contains(text(),'成交订单数')])[2]")
                self.sleep(1)
                self.click("(//span[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align')][contains(text(),'成交订单数')])[2]")
                self.sleep(2)
                meynumber1 = self.find_element('//*[@id="dilu_micro_root"]/div/div[5]/div/div/div[2]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[3]/div/div[1]').text
            # meynumber1 = '1,23'
                match2 = re.findall(r'(\d+)', meynumber1)
                mey_number1 = ""
                for num in match2:
                    mey_number1 += num
                mey_number1 = int(mey_number1)

                self.sleep(1)
                meynumber2 = self.find_element('//*[@id="dilu_micro_root"]/div/div[5]/div/div/div[2]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[3]/td[3]/div/div[1]').text
                match = re.findall(r'\d+', meynumber2)
                mey_number2 = ""
                for num in match:
                    mey_number2 += num
                mey_number2 = int(mey_number2)

                assert mey_number1 > mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.skip
    @pytest.mark.p0
    def test_trade_compose_real_reverse_tfc(self):
        self.checkout_trade_new()
        self.click(
            '/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]')
        self.sleep(3)
        self.click("//span[contains(text(),'按售卖载体拆分')]")

        xpathStr = '/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[{idx}]/div[1]'
        idx = str(random.choice([num for num in range(3, 8, 2)]))
        path = xpathStr.format(idx=idx)
        self.click("//div[@id='liveAmt']")
        self.sleep(5)
        self.click("(//span[contains(text(),'详情')])[2]")

        number = self.find_element(
            '//*[@id="sytWholeDealCarrier"]/div[2]/div[2]/div[1]/div[3]/div/div[1]/div[2]/div/span[1]').text
        match = number.replace(',', '')
        num1 = int(float(match))
        print(num1)

        if num1 == 0:
            return
        else:

            text = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[5]/div/div/div[2]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/ul/li[1]').text
            match = re.search(r'共 (\d+) 条', text)
            num = int(match.group(1))

            if num > 10:
                title1 = self.find_element(
                '//*[@id="dilu_micro_root"]/div/div[5]/div/div/div[2]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div[2]/div[2]/div[2]/div/span[1]').text


                self.click("(//li[contains(@title,'下一页')])[2]")
                self.sleep(3)
                title2 = self.find_element(
                '//*[@id="dilu_micro_root"]/div/div[5]/div/div/div[2]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div[2]/div[2]/div[2]/div/span[1]').text
                self.sleep(3)
                substr1 = title1[:11]
                substr2 = title2[:11]
                assert substr1 != substr2

    @pytest.mark.skip
    @pytest.mark.p0
    def test_trade_account_compose_desc_real(self):
        self.checkout_trade_new()
        self.click(
            '/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]')
        self.sleep(3)
        # xpathStr1 = '//*[@id="root"]/div/div/div[4]/div[1]/div[1]/div/div[1]/div/div/div[{idx}]'
        # idx1 = str(random.choice([num for num in range(1, 5)]))
        # path1 = xpathStr1.format(idx=idx1)
        self.click('//*[@id="dilu_micro_root"]/div/div[4]/div[1]/div[1]/div/div[1]/div/div/div[3]')
        xpathStr2 = '//*[@id="dilu_micro_root"]/div/div[4]/div[1]/div[1]/div/div[2]/div/div/div[{idx}]'
        idx2 = str(random.choice([num for num in range(1, 5)]))
        path2 = xpathStr2.format(idx=idx2)
        self.click(path2)
        self.sleep(3)

        # 成交订单数降序
        self.click(
            "(//span[@class='kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align'][contains(text(),'成交订单数')])[1]")
        self.sleep(1)
        self.click(
            "(//span[@class='kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align'][contains(text(),'成交订单数')])[1]")
        self.sleep(2)
        meynumber1 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[4]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[4]/div').text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[4]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[4]/div').text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    @pytest.mark.skip
    @pytest.mark.p0
    def test_trade_account_compose_reverse_real(self):
        self.checkout_trade_new()
        self.click(
            '/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]')
        self.sleep(3)

        xpathStr1 = '//*[@id="dilu_micro_root"]/div/div[4]/div[1]/div[1]/div/div[1]/div/div/div[{idx}]'
        idx1 = str(random.choice([num for num in range(1, 5)]))
        path1 = xpathStr1.format(idx=idx1)
        self.click(path1)
        xpathStr2 = '//*[@id="dilu_micro_root"]/div/div[4]/div[1]/div[1]/div/div[2]/div/div/div[{idx}]'
        idx2 = str(random.choice([num for num in range(1, 5)]))
        path2 = xpathStr2.format(idx=idx2)
        self.click(path2)
        self.sleep(3)

        text = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[4]/div[2]/div/div/div/div/div/div/div/div/div/ul/li[1]').text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:
            title1 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[4]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div/div[2]/div/div/span[1]').text

            self.click("(//li[@title='下一页'])[1]")
            self.sleep(3)
            title2 = self.find_element(
                '//*[@id="dilu_micro_root"]/div/div[4]/div[2]/div/div/div/div/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[1]/div/div/div[2]/div/div/span[1]').text
            self.sleep(3)
            substr1 = title1[:8]
            substr2 = title2[:8]
            assert substr1 != substr2

    #自建商品
    @pytest.mark.p0
    def test_trade_overView_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)
        real_info1 = self.get_text(
            "(//div[@class='kwaishop-tianhe-tradeManagement-pc-pro-checkableTags-wrapper'])[2]")
        self.assert_in("全部", real_info1)
        self.assert_in("直播间", real_info1)
        self.assert_in("商品卡", real_info1)
        self.assert_in("短视频", real_info1)

    #核心指标
    @pytest.mark.p0
    def test_trade_core_overView_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)

        real_info1 = self.get_text(
            "//div[contains(@class,'Lu_uagW5KraNOPLN4n8d')]")
        self.assert_in("成交金额", real_info1)
        self.assert_in("退款金额（支付日）", real_info1)
        self.assert_in("退款金额（退款日）", real_info1)
        self.assert_in("成交订单数", real_info1)
        self.assert_in("成交人数", real_info1)
        self.assert_in("客单价", real_info1)


    #核心指标
    @pytest.mark.p0
    def test_trade_core_overView_live_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)

        self.click("(//span[contains(text(),'直播间')])[1]")
        self.sleep(3)
        real_info1 = self.get_text(
            "//div[contains(@class,'Lu_uagW5KraNOPLN4n8d')]")
        self.assert_in("成交金额", real_info1)
        self.assert_in("退款金额（支付日）", real_info1)
        self.assert_in("退款金额（退款日）", real_info1)

    #载体构成-售卖方式拆分
    @pytest.mark.p0
    def test_trade_compose_1_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)

        real_info1 = self.get_text("//div[@class='kpro-data-calculation-normal-cards']")
        self.assert_in("整体成交", real_info1)
        self.assert_in("自营成交", real_info1)
        self.assert_in("达人合作成交", real_info1)
        self.assert_in("超级链接成交", real_info1)

        real_info2 = self.get_text("//div[@class='kpro-data-calculation-simple-cards']")
        self.assert_in("直播间", real_info2)
        self.assert_in("短视频", real_info2)
        self.assert_in("商品卡", real_info2)
        self.assert_in("其他", real_info2)

        xpathStr = '//*[@id="selfAmt"]/div'
        # idx = str(random.choice([num for num in range(1, 9, 2)]))
        # path = xpathStr.format(idx=idx)
        # self.click(path)
        # self.sleep(2)
        real_info2 = self.get_text("//div[@class='kpro-data-calculation-simple-cards']")
        self.assert_in("直播间", real_info2)
        self.assert_in("短视频", real_info2)
        self.assert_in("商品卡", real_info2)
        self.assert_in("其他", real_info2)
        self.assert_element("//div[@id='sytWholeDealCarrier']//div//div[contains(@customstyle,'[object Object]')]//div[contains(@customstyle,'[object Object]')]//div//div//div//canvas")

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_trade_compose_live_desc_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)

        self.click("(//span[contains(text(),'详情')])[1]")
        # 成交订单数降序
        self.click(
            "(//span[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align')][contains(text(),'成交订单数')])[2]")
        self.sleep(1)
        self.click(
            "(//span[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align')][contains(text(),'成交订单数')])[2]")
        self.sleep(2)
        meynumber1 = self.find_element(
            TradePage.trade_detail_l1).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            TradePage.trade_detail_l2).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_trade_compose_live_reverse_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)

        self.click("(//span[contains(text(),'详情')])[1]")

        text = self.find_element(
            TradePage.trade_detail_num).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:

            title1 = self.find_element(
            TradePage.trade_detail_zhibojiang_title).text

            self.click(TradePage.trade_detail_next)
            self.sleep(3)
            title2 = self.find_element(
                TradePage.trade_detail_zhibojiang_title).text
            self.sleep(3)
            substr1 = title1[:11]
            substr2 = title2[:11]
            assert substr1 != substr2

    @pytest.mark.p0
    def test_trade_compose_photo_desc_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)

        self.click("(//span[contains(text(),'详情')])[2]")
        text = self.find_element(
            TradePage.trade_detail_num).text
        match = re.search(r'共 (\d+) 条', text)
        num1 = int(match.group(1))

        if num1 == 0:
            return
        else:
        # 成交订单数降序
            self.click(
            "(//span[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align')][contains(text(),'成交订单数')])[2]")
            self.sleep(1)
            self.click(
            "(//span[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align')][contains(text(),'成交订单数')])[2]")
            self.sleep(2)
            meynumber1 = self.find_element(
            TradePage.trade_detail_l1).text
        # meynumber1 = '1,23'
            match2 = re.findall(r'(\d+)', meynumber1)
            mey_number1 = ""
            for num in match2:
                mey_number1 += num
            mey_number1 = int(mey_number1)

            self.sleep(1)
            meynumber2 = self.find_element(
            TradePage.trade_detail_l2).text
            match = re.findall(r'\d+', meynumber2)
            mey_number2 = ""
            for num in match:
                mey_number2 += num
            mey_number2 = int(mey_number2)

            assert mey_number1 >= mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_trade_compose_photo_reverse_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)

        self.click("(//span[contains(text(),'详情')])[2]")

        text = self.find_element(
            TradePage.trade_detail_num).text
        match = re.search(r'共 (\d+) 条', text)
        num1 = int(match.group(1))

        if num1 == 0:
            return
        else:
            text = self.find_element(
               TradePage.trade_detail_num).text
            match = re.search(r'共 (\d+) 条', text)
            num = int(match.group(1))
            if num > 10:

                title1 = self.find_element(TradePage.trade_detail_photo_id).text

                self.click(TradePage.trade_detail_next)
                self.sleep(3)
                title2 = self.find_element(TradePage.trade_detail_photo_id).text
                self.sleep(3)
                substr1 = title1[:11]
                substr2 = title2[:11]
                assert substr1 != substr2

    @pytest.mark.p0
    def test_trade_compose_goods_desc_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)

        self.click("(//span[contains(text(),'详情')])[3]")
        # 成交订单数降序
        self.click(
            "(//span[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align')][contains(text(),'成交订单数')])[2]")
        self.sleep(1)
        self.click(
            "(//span[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align')][contains(text(),'成交订单数')])[2]")
        self.sleep(2)
        meynumber1 = self.find_element(
            TradePage.trade_detail_card_l1).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            TradePage.trade_detail_card_l2        ).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 >= mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_trade_compose_goods_reverse_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)

        self.click("(//span[contains(text(),'详情')])[3]")

        text = self.find_element(
            TradePage.trade_detail_num).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:
            title1 = self.find_element(TradePage.trade_detail_card_title).text

            self.click(TradePage.trade_detail_next)
            self.sleep(3)
            title2 = self.find_element(TradePage.trade_detail_card_title).text
            self.sleep(3)
            substr1 = title1[:11]
            substr2 = title2[:11]
            assert substr1 != substr2

    #载体构成-售卖载体拆分
    @pytest.mark.p0
    def test_trade_compose_2_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)

        #载体直播间成交
        self.click('//*[@id="liveAmt"]')
        # xpathStr = '/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[{idx}]/div[1]'
        # idx = str(random.choice([num for num in range(1, 8, 2)]))
        # path = xpathStr.format(idx=idx)
        # self.click(path)
        # self.sleep(2)
        real_info2 = self.get_text('/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div[2]/div[3]/div[2]/div[2]/div[1]')
        self.assert_in("自营", real_info2)
        self.assert_in("达人合作", real_info2)
        self.assert_in("超级链接", real_info2)
        self.assert_element("//div[@id='sytWholeDealCarrier']//div//div[contains(@customstyle,'[object Object]')]//div[contains(@customstyle,'[object Object]')]//div//div//div//canvas")

    @pytest.mark.p0
    def test_trade_compose_self_desc_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)
        #载体直播间
        self.click('//*[@id="liveAmt"]')
        self.click('/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div[2]/div[3]/div[2]/div[2]/div[1]/div[1]/div/div[1]/div[1]/div[1]/span')
        # 成交订单数降序
        self.click(
            "(//span[@class='kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align'][contains(text(),'成交订单数')])[2]")
        self.sleep(1)
        self.click(
            "(//span[@class='kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align'][contains(text(),'成交订单数')])[2]")
        self.sleep(2)
        meynumber1 = self.find_element(
            TradePage.trade_detail_l1).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            TradePage.trade_detail_l2        ).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_trade_compose_self_reverse_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)

        # 载体直播间
        self.click('//*[@id="liveAmt"]')
        self.click(
            '/html/body/div[1]/div[1]/div/section/section/main/div[2]/div/div/div/div[2]/div[3]/div[2]/div[2]/div[1]/div[1]/div/div[1]/div[1]/div[1]/span')
        text = self.find_element(
            TradePage.trade_detail_num).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:

            title1 = self.find_element(
            TradePage.trade_detail_zhibojiang_title).text

            self.click( TradePage.trade_detail_next)
            self.sleep(3)
            title2 = self.find_element(
                TradePage.trade_detail_zhibojiang_title ).text
            self.sleep(3)
            assert title1 != title2

    @pytest.mark.skip
    @pytest.mark.p0
    def test_trade_compose_cooperate_desc_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)

        self.click("//span[contains(text(),'按售卖载体拆分')]")
        self.click("(//span[contains(text(),'详情')])[2]")
        text = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[5]/div/div/div[2]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/ul/li[1]').text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 2:
        # 成交订单数降序
            self.click("(//span[@class='kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align'][contains(text(),'成交订单数')])[2]")
            self.sleep(1)
            self.click("(//span[@class='kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align'][contains(text(),'成交订单数')])[2]")
            self.sleep(2)
            meynumber1 = self.find_element('//*[@id="dilu_micro_root"]/div/div[5]/div/div/div[2]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[4]/div/div[1]').text
        # meynumber1 = '1,23'
            match2 = re.findall(r'(\d+)', meynumber1)
            mey_number1 = ""
            for num in match2:
                mey_number1 += num
            mey_number1 = int(mey_number1)

            self.sleep(1)
            meynumber2 = self.find_element('//*[@id="dilu_micro_root"]/div/div[5]/div/div/div[2]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[3]/td[4]/div/div[1]').text
            match = re.findall(r'\d+', meynumber2)
            mey_number2 = ""
            for num in match:
                mey_number2 += num
            mey_number2 = int(mey_number2)

            assert mey_number1 > mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.skip
    @pytest.mark.p0
    def test_trade_compose_cooperate_reverse_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)

        self.click("//span[contains(text(),'按售卖载体拆分')]")
        self.click("(//span[contains(text(),'详情')])[2]")

        text = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[5]/div/div/div[2]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/ul/li[1]').text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:

            title1 = self.find_element(
            '/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[5]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[2]/td[1]/div[1]/div[2]/div[2]/div[2]/div[1]/span[1]').text


            self.click("(//li[contains(@title,'下一页')])[2]")
            self.sleep(3)
            title2 = self.find_element(
            '/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[5]/div[1]/div[1]/div[2]/div[1]/div[1]/div[2]/div[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[1]/div[2]/table[1]/tbody[1]/tr[3]/td[1]/div[1]/div[2]/div[2]/div[2]/div[1]/span[1]').text
            self.sleep(3)
            substr1 = title1[:11]
            substr2 = title2[:11]
            assert substr1 != substr2

    #账号构成
    @pytest.mark.p0
    def test_trade_account_compose_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)


        # xpathStr1 = '//*[@id="dilu_micro_root"]/div/div[4]/div[1]/div[1]/div/div[1]/div/div/div[{idx}]'
        # idx1 = str(random.choice([num for num in range(1, 5)]))
        # path1 = xpathStr1.format(idx=idx1)
        # self.click(path1)
        # xpathStr2 = '//*[@id="dilu_micro_root"]/div/div[4]/div[1]/div[1]/div/div[2]/div/div/div[{idx}]'
        # idx2 = str(random.choice([num for num in range(1, 5)]))
        # path2 = xpathStr2.format(idx=idx2)
        self.click('//*[@id="dilu_micro_root"]/div/div[2]/div[4]/div[2]/div[1]/div/div[2]/div/div/div[2]/div/div/span[1]')
        self.sleep(3)
        # 直播间
        real_info1 = self.get_text('//*[@id="dilu_micro_root"]/div/div[2]/div[4]/div[3]/div/div/div/div/div/div/div/div/div/div/div/div/table/thead')
        self.assert_in("成交金额（元）", real_info1)
        self.assert_in("退款金额（支付日）", real_info1)
        self.assert_in("成交订单数", real_info1)
        self.assert_in("成交人数", real_info1)
        self.assert_in("客单价", real_info1)
        self.assert_in("近7天成交金额趋势", real_info1)

    @pytest.mark.p0
    def test_trade_account_compose_desc_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)

        # xpathStr1 = '//*[@id="root"]/div/div/div[4]/div[1]/div[1]/div/div[1]/div/div/div[{idx}]'
        # idx1 = str(random.choice([num for num in range(1, 5)]))
        # path1 = xpathStr1.format(idx=idx1)
        # self.click(path1)

        self.click('//*[@id="dilu_micro_root"]/div/div[2]/div[4]/div[2]/div[1]/div/div[2]/div/div/div[2]/div/div/span[1]')
        self.sleep(3)
        # 成交订单数降序
        self.click(
            "(//span[@class='kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align'][contains(text(),'成交订单数')])[1]")
        self.sleep(1)
        self.click(
            "(//span[@class='kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align'][contains(text(),'成交订单数')])[1]")
        self.sleep(2)
        meynumber1 = self.find_element(
            TradePage.trade_account_l1).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
           TradePage.trade_account_l2).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2


    @pytest.mark.p0
    def test_trade_account_compose_reverse_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)

        self.click(
            '//*[@id="dilu_micro_root"]/div/div[2]/div[4]/div[2]/div[1]/div/div[2]/div/div/div[2]/div/div/span[1]')
        self.sleep(3)

        text = self.find_element(
            TradePage.trade_account_num).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:
            title1 = self.find_element(
            TradePage.trade_account_title).text

            self.click(TradePage.trade_account_next)
            self.sleep(3)
            title2 = self.find_element(
            TradePage.trade_account_title).text
            self.sleep(3)
            substr1 = title1[:8]
            substr2 = title2[:8]
            assert substr1 != substr2

    # 自建-实时
    @pytest.mark.p0
    def test_trade_core_overView_real_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)
        self.click('//*[@id="compareQuickActive"]')
        self.sleep(3)
        real_info1 = self.get_text(
            "//div[contains(@class,'Lu_uagW5KraNOPLN4n8d')]")
        self.assert_in("成交金额", real_info1)
        self.assert_in("退款金额（支付日）", real_info1)
        self.assert_in("退款金额（退款日）", real_info1)


    @pytest.mark.p0
    def test_trade_compose_real_desc_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)
        self.click(TradePage.trade_date_real)
        self.sleep(3)

        # xpathStr = '/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[5]/div[1]'
        # idx = str(random.choice([num for num in range(3, 6, 2)]))
        # path = xpathStr.format(idx=idx)
        # self.click(xpathStr)
        # self.sleep(2)

        self.click("(//span[contains(text(),'详情')])[3]")
        # 成交订单数降序
        self.click(
            "(//span[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align')][contains(text(),'成交订单数')])[2]")
        self.sleep(1)
        self.click(
            "(//span[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align')][contains(text(),'成交订单数')])[2]")
        self.sleep(2)
        meynumber1 = self.find_element(
           TradePage.trade_detail_l1).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
          TradePage.trade_detail_l2).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 >= mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.p0
    def test_trade_compose_real_reverse_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)
        self.click(TradePage.trade_date_real)
        self.sleep(3)

        # xpathStr = '/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[{idx}]/div[1]'
        # idx = str(random.choice([num for num in range(3, 6, 2)]))
        # path = xpathStr.format(idx=idx)
        # self.click(path)
        # self.sleep(2)
        self.click("(//span[contains(text(),'详情')])[3]")
        text = self.find_element(
           TradePage.trade_detail_num).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:

            title1 = self.find_element(
           TradePage.trade_detail_card_title).text

            self.click(TradePage.trade_detail_next)
            self.sleep(3)
            title2 = self.find_element(
                TradePage.trade_detail_card_title
            ).text
            self.sleep(3)
            substr1 = title1[:11]
            substr2 = title2[:11]
            assert substr1 != substr2
    @pytest.mark.skip
    @pytest.mark.p0
    def test_trade_compose_real_desc_tfc_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)

        self.click(
            '/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]')
        self.sleep(5)
        self.click("//span[contains(text(),'按售卖载体拆分')]")

        xpathStr = '/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[{idx}]/div[1]'
        idx = str(random.choice([num for num in range(3, 8, 2)]))
        path = xpathStr.format(idx=idx)
        self.click(path)
        self.sleep(2)
        self.click("(//span[contains(text(),'详情')])[2]")
        number = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[3]/div[2]/div[2]/div[1]/div[3]/div/div[1]/div[2]/div/span[1]').text
        # match = re.search(r'共 (\d+) 条', number)
        match = number.replace(',', '')
        num1 = int(float(match))

        if num1 == 0:
            return
        else:
            # 成交订单数降序
            self.click("(//span[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align')][contains(text(),'成交订单数')])[2]")
            self.sleep(1)
            self.click("(//span[contains(@class,'kwaishop-tianhe-tradeManagement-pc-table-column-title-no-align')][contains(text(),'成交订单数')])[2]")
            self.sleep(2)
            meynumber1 = self.find_element('//*[@id="dilu_micro_root"]/div/div[5]/div/div/div[2]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[4]/div').text
            # meynumber1 = '1,23'
            match2 = re.findall(r'(\d+)', meynumber1)
            mey_number1 = ""
            for num in match2:
                mey_number1 += num
            mey_number1 = int(mey_number1)

            self.sleep(1)
            meynumber2 = self.find_element('//*[@id="dilu_micro_root"]/div/div[5]/div/div/div[2]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[4]/td[4]/div').text
            match = re.findall(r'\d+', meynumber2)
            mey_number2 = ""
            for num in match:
                mey_number2 += num
            mey_number2 = int(mey_number2)

            assert mey_number1 > mey_number2

    # 载体构成-售卖方式拆分-载体二级页
    @pytest.mark.skip
    @pytest.mark.p0
    def test_trade_compose_real_reverse_tfc_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)
        self.click(
            '/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[1]/div[2]/div[2]/div[1]/div[1]')
        self.sleep(3)
        self.click("//span[contains(text(),'按售卖载体拆分')]")
        self.sleep(5)

        xpathStr = '/html[1]/body[1]/div[1]/div[1]/div[1]/section[1]/section[1]/main[1]/div[2]/div[1]/div[1]/div[1]/div[3]/div[2]/div[1]/div[1]/div[{idx}]/div[1]'
        idx = str(random.choice([num for num in range(3, 8, 2)]))
        path = xpathStr.format(idx=idx)
        self.click(path)
        self.sleep(5)
        self.click("(//span[contains(text(),'详情')])[2]")

        text = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[5]/div/div/div[2]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/ul/li[1]').text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:

            title1 = self.find_element(
            '//*[@id="dilu_micro_root"]/div/div[5]/div/div/div[2]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div[2]/div[2]/div[2]/div/span[1]').text

            self.click("(//li[contains(@title,'下一页')])[2]")
            self.sleep(3)
            title2 = self.find_element(
                '//*[@id="dilu_micro_root"]/div/div[5]/div/div/div[2]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div[2]/table/tbody/tr[2]/td[1]/div/div[2]/div[2]/div[2]/div/span[1]').text
            self.sleep(3)
            substr1 = title1[:11]
            substr2 = title2[:11]
            assert substr1 != substr2

    @pytest.mark.p0
    def test_trade_account_compose_desc_real_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)
        self.click(TradePage.trade_date_real)
        self.sleep(3)
        # xpathStr1 = '//*[@id="root"]/div/div/div[4]/div[1]/div[1]/div/div[1]/div/div/div[{idx}]'
        # idx1 = str(random.choice([num for num in range(1, 5)]))
        # path1 = xpathStr1.format(idx=idx1)
        # self.click('//*[@id="dilu_micro_root"]/div/div[4]/div[1]/div[1]/div/div[1]/div/div/div[3]')
        # xpathStr2 = '//*[@id="dilu_micro_root"]/div/div[4]/div[1]/div[1]/div/div[1]/div/div/div[{idx}]'
        # idx2 = str(random.choice([num for num in range(1, 5)]))
        # path2 = xpathStr2.format(idx=idx2)

        self.click('//*[@id="sytWholeDealCarrier"]/div[2]/div[2]/div[1]/div[1]/div/div[1]/div[1]/div[1]/span')
        self.sleep(5)

        # 成交订单数降序
        self.click('//*[@id="dilu_micro_root"]/div/div[3]/div/div/div[2]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div[1]/table/thead/tr/th[3]/div[1]/span[2]/span/span[2]')
        self.sleep(1)
        self.click(
            '//*[@id="dilu_micro_root"]/div/div[3]/div/div/div[2]/div/div/div[2]/div/div[2]/div/div/div/div/div/div/div/div/div/div/div[1]/table/thead/tr/th[3]/div[1]/span[2]/span/span[2]')
        self.sleep(2)
        meynumber1 = self.find_element(
            TradePage.trade_detail_l1).text
        # meynumber1 = '1,23'
        match2 = re.findall(r'(\d+)', meynumber1)
        mey_number1 = ""
        for num in match2:
            mey_number1 += num
        mey_number1 = int(mey_number1)

        self.sleep(1)
        meynumber2 = self.find_element(
            TradePage.trade_detail_l2).text
        match = re.findall(r'\d+', meynumber2)
        mey_number2 = ""
        for num in match:
            mey_number2 += num
        mey_number2 = int(mey_number2)

        assert mey_number1 > mey_number2


    @pytest.mark.p0
    def test_trade_account_compose_reverse_real_selfprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'自建商品')]")
        self.sleep(5)
        self.click(TradePage.trade_date_real)
        self.sleep(3)

        # xpathStr1 = '//*[@id="dilu_micro_root"]/div/div[4]/div[1]/div[1]/div/div[1]/div/div/div[{idx}]'
        # idx1 = str(random.choice([num for num in range(1, 5)]))
        # path1 = xpathStr1.format(idx=idx1)
        # self.click(path1)
        # xpathStr2 = '//*[@id="dilu_micro_root"]/div/div[4]/div[1]/div[1]/div/div[2]/div/div/div[{idx}]'
        # idx2 = str(random.choice([num for num in range(1, 5)]))
        # path2 = xpathStr2.format(idx=idx2)
        # self.click(path2)
        # self.sleep(3)
        self.click('//*[@id="sytWholeDealCarrier"]/div[2]/div[2]/div[1]/div[1]/div/div[1]/div[1]/div[1]/span')
        self.sleep(5)

        text = self.find_element(TradePage.trade_detail_num).text
        match = re.search(r'共 (\d+) 条', text)
        num = int(match.group(1))
        if num > 10:
            title1 = self.find_element(
           TradePage.trade_detail_zhibojiang_title).text
            self.click(TradePage.trade_detail_next)
            self.sleep(3)
            title2 = self.find_element(
                TradePage.trade_detail_zhibojiang_title ).text
            self.sleep(3)
            substr1 = title1[:8]
            substr2 = title2[:8]
            assert substr1 != substr2

    #他人商品
    @pytest.mark.p0
    def test_trade_overView_otherprovide(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'他人商品')]")
        self.sleep(5)
        xpathStr2 = '//*[@id="sytWholeDealOverview"]/div[1]/div[1]/div/div/div/div/div[{idx}]'
        idx2 = str(random.choice([num for num in range(1, 5)]))
        path2 = xpathStr2.format(idx=idx2)
        self.click(path2)
        self.sleep(3)

        real_info1 = self.get_text(
            "//div[contains(@class,'Lu_uagW5KraNOPLN4n8d')]")
        self.assert_in("成交金额", real_info1)
        self.assert_in("退款金额（支付日）", real_info1)
        self.assert_in("退款金额（退款日）", real_info1)

    @pytest.mark.p0
    def test_trade_overView_otherprovide_real(self):
        self.checkout_trade_new()
        self.click("//div[@class='wrapper-div___B6EFL']")
        self.click("//div[contains(@title,'他人商品')]")
        self.sleep(5)
        self.click(
            TradePage.trade_date_real)
        self.sleep(3)

        xpathStr2 = '//*[@id="sytWholeDealOverview"]/div[1]/div[1]/div/div/div/div/div[{idx}]'
        idx2 = str(random.choice([num for num in range(1, 5)]))
        path2 = xpathStr2.format(idx=idx2)
        self.click(path2)
        self.sleep(3)

        real_info1 = self.get_text(
            "//div[contains(@class,'Lu_uagW5KraNOPLN4n8d')]")
        self.assert_in("成交金额", real_info1)
        self.assert_in("退款金额（支付日）", real_info1)
        self.assert_in("退款金额（退款日）", real_info1)













