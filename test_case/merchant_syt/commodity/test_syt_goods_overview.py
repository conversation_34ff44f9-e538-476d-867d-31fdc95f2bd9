from unittest import skip

import pytest
from ddt import ddt

from utils.http_help import BaseHttpRequest
from test_case.merchant_syt.base import BaseTestCase

account_proxy_app = BaseHttpRequest(user_id="B_**********")

class TestSytGoodsOverview(BaseTestCase):
    def checkout_as_module(self):
        self.login("SYT_DOMAIN", "supply_account")
        # 点击服务 tab
        self.driver.maximize_window()
        account_proxy_app.account_proxy_confirm(**********, *********)
        self.refresh()
        self.sleep(1)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        # 首页诊断弹窗
        if self.is_element_visible("//button[contains(text(),'关闭')]"):
            self.click("//button[contains(text(),'关闭')]")
        self.sleep(1)
        self.refresh()
        if self.is_element_visible("(//span[contains(text(),'立即查看')])[1]"):
            self.click("(//span[contains(text(),'立即查看')])[1]")
            self.switch_to_window(1)
            self.switch_to_window(0)
            self.sleep(1)
        # self.click("//span[@class='syt-main-dropdown-trigger w20sdnf3cIh6Yo7rYRwE']")
        self.find_element("(//span[contains(text(),'商品卡')])[1]", by="xpath").click()
        # self.click("//span[contains(text(),'服务')]")
        self.sleep(1)
    """
        商品卡概览
    """
    def test_syt_goods_overview(self):
        self.checkout_as_module()
        self.sleep(3)
        self.click("//span[contains(text(),'商品卡概览')]")
        self.sleep(3)
        if self.is_element_visible("//span[contains(text(),'展开')]"):
            #self.find_element("//span[contains(text(),'展开')]",by="xpath").click()
            self.assert_element("//span[contains(text(),'查看商品卡说明')]", by="xpath")
        if self.is_element_visible("//span[contains(text(),'收起')]"):
            self.find_element("//span[contains(text(),'收起')]",by="xpath").click()
            self.assert_element("//span[contains(text(),'查看商品卡说明')]", by="xpath")
        self.sleep(3)
        self.assert_element("//span[contains(text(),'核心数据')]",by="xpath")
        self.assert_element("//span[contains(text(),'商品卡成交金额')]",by="xpath")
        self.assert_element("//span[contains(text(),'商品卡成交订单数')]",by="xpath")
        self.assert_element("//span[contains(text(),'商品卡退款金额')]",by="xpath")
        self.assert_element("//span[contains(text(),'商品卡曝光次数')]",by="xpath")
        self.assert_element("//span[contains(text(),'商品卡点击次数')]",by="xpath")
        self.assert_element("//span[contains(text(),'曝光点击率')]",by="xpath")
        """
            时间切换
        """
        self.find_element("//div[contains(text(),'近7天')]",by="xpath").click()
        self.assert_element("//span[contains(text(),'商品卡成交金额')]", by="xpath")
        self.assert_element("//span[contains(text(),'商品卡成交订单数')]", by="xpath")
        self.assert_element("//span[contains(text(),'商品卡退款金额')]", by="xpath")
        self.assert_element("//span[contains(text(),'商品卡曝光次数')]", by="xpath")

        """
            趋势图校验
        """
        self.find_element("//span[contains(text(),'商品卡成交订单数')]",by="xpath").click()
        self.assert_element("(//canvas)[1]", by="xpath")
        self.assert_element("//span[contains(text(),'流量来源')]", by="xpath")
        self.find_element("//span[contains(text(),'指标配置')]",by="xpath").click()
        self.sleep(1)
        self.find_element("//span[contains(text(),'取 消')]",by="xpath").click()
        #self.assert_element("//div[contains(text(),'店铺页')]", by="xpath")
        #self.assert_element("//div[contains(text(),'商城页')]", by="xpath")
        self.assert_element("//span[contains(text(),'商品列表')]", by="xpath")
        self.find_element("//input[@placeholder='搜索商品标题/ID']",by="xpath").send_keys("1")
        self.sleep(1)
        self.assert_element("//th[contains(text(),'商品信息')]", by="xpath")

    """
        流量来源明细
    """
    def test_syt_goods_flow_detail(self):
        self.checkout_as_module()
        self.sleep(3)
        self.click("//span[contains(text(),'流量来源明细')]")
        self.sleep(3)
        self.find_element("//span[contains(text(),'查看商品卡说明')]",by="xpath").click()
        self.switch_to_window(0)
        self.sleep(3)
        self.assert_element("(//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-tabs-tab'])[1]",by="xpath")
        self.assert_element("//span[contains(text(),'商品卡成交金额')]",by="xpath")
        self.find_element("(//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-tabs-tab'])[2]",by="xpath").click()
        self.assert_element("//span[contains(text(),'商品卡成交金额')]",by="xpath")
        self.find_element("(//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-tabs-tab'])[3]",by="xpath").click()
        self.assert_element("//span[contains(text(),'商品卡成交金额')]",by="xpath")
        self.find_element("(//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-tabs-tab'])[4]",by="xpath").click()
        self.assert_element("//span[contains(text(),'商品卡成交金额')]",by="xpath")
        self.find_element("(//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-tabs-tab'])[5]",by="xpath").click()
        self.assert_element("//span[contains(text(),'商品卡成交金额')]",by="xpath")
        self.find_element("(//div[@class='kwaishop-tianhe-data-goods-card-manage-pc-tabs-tab'])[6]",by="xpath").click()
        self.assert_element("//span[contains(text(),'商品卡成交金额')]",by="xpath")
        self.assert_element("(//span[contains(text(),'下载数据')])[1]",by="xpath")
        #self.assert_element("//span[contains(text(),'商品列表')]", by="xpath")
        self.find_element("//input[@placeholder='搜索商品标题/ID']", by="xpath").send_keys("1")
        self.sleep(1)
        self.assert_element("//th[contains(text(),'商品信息')]", by="xpath")

    """
        商品卡商品榜
    """

    def test_syt_goods_rank(self):
        self.checkout_as_module()
        self.sleep(3)
        self.click("//span[contains(text(),'商品卡商品榜')]")
        self.sleep(5)
        real_info1 = self.get_text("//div[@class='kwaishop-tianhe-ranking-management-pc-table-content']")
        self.assert_in("成交金额", real_info1)
        self.assert_in("点击次数", real_info1)
        self.assert_in("成长增速", real_info1)
        self.assert_in("转化率", real_info1)
        self.assert_in("商品信息", real_info1)
        self.assert_in("所属店铺", real_info1)
































