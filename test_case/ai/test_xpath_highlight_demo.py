#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
xpath元素定位和高亮功能演示测试用例
展示如何在实际测试中使用元素高亮功能
"""

import pytest
import time
import logging
from test_case.growth.kuaishouxiaodian.base import BaseTestCase

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TestXpathHighlightDemo(BaseTestCase):
    """xpath元素定位和高亮功能演示测试类"""
    
    def setUp(self, **kwargs):
        """测试前置设置"""
        super().setUp()
        self.maximize_window()
        logger.info("=== 开始xpath元素定位和高亮功能演示 ===")
    
    def tearDown(self):
        """测试后置清理"""
        # 清除所有高亮
        self.clear_all_highlights()
        logger.info("=== xpath高亮功能演示结束 ===")
        super().tearDown()
    
    @pytest.mark.demo
    def test_basic_xpath_highlight(self):
        """基础xpath元素定位和高亮演示"""
        logger.info("--- 测试1: 基础xpath元素定位和高亮 ---")
        
        # 打开百度首页
        self.open("https://www.baidu.com")
        self.sleep(2)
        
        # 演示不同样式的高亮
        logger.info("正在演示不同样式的元素高亮...")
        
        # 1. 高亮搜索框 - 成功样式（绿色）
        result1 = self.highlight_element_by_xpath(
            xpath="//input[@id='kw']",
            style_type="success",
            label_text="搜索输入框"
        )
        assert result1["success"], "搜索框高亮失败"
        logger.info("✅ 搜索框高亮成功（绿色边框）")
        self.sleep(1)
        
        # 2. 高亮搜索按钮 - 主要样式（蓝色）
        result2 = self.highlight_element_by_xpath(
            xpath="//input[@id='su']",
            style_type="primary", 
            label_text="搜索按钮"
        )
        assert result2["success"], "搜索按钮高亮失败"
        logger.info("✅ 搜索按钮高亮成功（蓝色边框）")
        self.sleep(1)
        
        # 3. 高亮导航链接 - 信息样式（青色）
        nav_links = [
            ("//a[contains(text(), '新闻')]", "新闻链接"),
            ("//a[contains(text(), '贴吧')]", "贴吧链接"),
            ("//a[contains(text(), '知道')]", "知道链接")
        ]
        
        for xpath, label in nav_links:
            result = self.highlight_element_by_xpath(
                xpath=xpath,
                style_type="info",
                label_text=label
            )
            if result["success"]:
                logger.info(f"✅ {label}高亮成功（青色边框）")
            else:
                logger.warning(f"⚠️ {label}高亮失败")
            self.sleep(0.5)
        
        # 等待观察效果
        self.sleep(3)
        
        # 清除所有高亮
        self.clear_all_highlights()
        logger.info("已清除所有高亮效果")
    
    @pytest.mark.demo
    def test_interactive_xpath_highlight(self):
        """交互式xpath元素定位和高亮演示"""
        logger.info("--- 测试2: 交互式元素操作和高亮 ---")
        
        # 打开百度首页
        self.open("https://www.baidu.com")
        self.sleep(2)
        
        # 演示带高亮的交互操作
        logger.info("正在演示带高亮的交互操作...")
        
        # 1. 高亮并点击搜索框
        logger.info("步骤1: 高亮并点击搜索框")
        self.click_with_highlight(
            xpath="//input[@id='kw']",
            style_type="warning",
            label_text="点击搜索框",
            highlight_duration=1
        )
        
        # 2. 高亮并输入搜索内容
        logger.info("步骤2: 高亮并输入搜索内容")
        self.type_with_highlight(
            xpath="//input[@id='kw']",
            text="Selenium自动化测试",
            style_type="info",
            label_text="输入搜索关键词",
            highlight_duration=1
        )
        
        # 3. 高亮并点击搜索按钮
        logger.info("步骤3: 高亮并点击搜索按钮")
        self.click_with_highlight(
            xpath="//input[@id='su']",
            style_type="primary",
            label_text="执行搜索",
            highlight_duration=1
        )
        
        # 等待搜索结果加载
        self.sleep(3)
        
        # 4. 高亮搜索结果
        logger.info("步骤4: 高亮搜索结果")
        search_results = [
            "//h3[@class='t']/a[1]",
            "(//h3[@class='t']/a)[2]", 
            "(//h3[@class='t']/a)[3]"
        ]
        
        for i, xpath in enumerate(search_results):
            result = self.highlight_element_by_xpath(
                xpath=xpath,
                style_type="success",
                label_text=f"搜索结果 {i+1}"
            )
            if result["success"]:
                logger.info(f"✅ 搜索结果 {i+1} 高亮成功")
                # 显示元素信息
                element_info = result.get("element_info", {})
                if element_info.get("text"):
                    logger.info(f"   标题: {element_info['text'][:50]}...")
            self.sleep(0.5)
        
        # 等待观察效果
        self.sleep(3)
    
    @pytest.mark.demo
    def test_batch_xpath_highlight(self):
        """批量xpath元素高亮演示"""
        logger.info("--- 测试3: 批量元素高亮 ---")
        
        # 打开GitHub首页
        self.open("https://www.github.com")
        self.sleep(3)
        
        # 批量高亮页面元素
        logger.info("正在批量高亮GitHub页面元素...")
        
        # 定义要高亮的元素列表
        github_elements = [
            "//a[contains(@class, 'HeaderMenu-link') and contains(text(), 'Product')]",
            "//a[contains(@class, 'HeaderMenu-link') and contains(text(), 'Solutions')]",
            "//a[contains(@class, 'HeaderMenu-link') and contains(text(), 'Open Source')]",
            "//a[contains(@class, 'HeaderMenu-link') and contains(text(), 'Pricing')]",
            "//a[contains(text(), 'Sign in')]",
            "//a[contains(text(), 'Sign up')]"
        ]
        
        # 批量高亮
        batch_result = self.highlight_multiple_elements(
            xpath_list=github_elements,
            style_type="info",
            auto_label=True
        )
        
        logger.info(f"批量高亮结果:")
        logger.info(f"  总计: {batch_result['total']} 个元素")
        logger.info(f"  成功: {batch_result['success_count']} 个")
        logger.info(f"  失败: {batch_result['failed_count']} 个")
        
        # 显示详细结果
        for i, result in enumerate(batch_result['results']):
            if result['success']:
                logger.info(f"  ✅ 元素 {i+1}: {result['xpath'][:50]}...")
            else:
                logger.warning(f"  ❌ 元素 {i+1}: {result['message']}")
        
        # 等待观察效果
        self.sleep(5)
    
    @pytest.mark.demo
    def test_error_handling_highlight(self):
        """错误处理和高亮演示"""
        logger.info("--- 测试4: 错误处理和高亮 ---")
        
        # 打开百度首页
        self.open("https://www.baidu.com")
        self.sleep(2)
        
        # 演示错误处理
        logger.info("正在演示错误处理...")
        
        # 1. 尝试高亮不存在的元素
        logger.info("测试1: 尝试高亮不存在的元素")
        result1 = self.highlight_element_by_xpath(
            xpath="//div[@id='non-existent-element']",
            style_type="error",
            label_text="不存在的元素",
            timeout=3
        )
        
        assert not result1["success"], "应该返回失败结果"
        logger.info(f"✅ 正确处理了不存在的元素: {result1['message']}")
        
        # 2. 高亮存在的元素用于对比
        logger.info("测试2: 高亮存在的元素用于对比")
        result2 = self.highlight_element_by_xpath(
            xpath="//input[@id='kw']",
            style_type="success",
            label_text="存在的元素"
        )
        
        assert result2["success"], "应该成功高亮存在的元素"
        logger.info("✅ 成功高亮存在的元素")
        
        # 3. 演示临时高亮（自动消失）
        logger.info("测试3: 临时高亮效果（3秒后自动消失）")
        result3 = self.highlight_element_by_xpath(
            xpath="//input[@id='su']",
            style_type="warning",
            label_text="临时高亮(3秒)",
            duration=3
        )
        
        assert result3["success"], "临时高亮应该成功"
        logger.info("✅ 临时高亮已设置，将在3秒后自动消失")
        
        # 等待临时高亮消失
        self.sleep(4)
        logger.info("✅ 临时高亮已自动消失")
    
    @pytest.mark.demo
    def test_element_verification_with_highlight(self):
        """元素验证和高亮演示"""
        logger.info("--- 测试5: 元素验证和高亮 ---")
        
        # 打开百度首页
        self.open("https://www.baidu.com")
        self.sleep(2)
        
        # 演示元素验证和高亮
        logger.info("正在演示元素验证和高亮...")
        
        # 1. 验证并高亮搜索框
        logger.info("验证搜索框是否可见并高亮")
        try:
            self.assert_element_visible_with_highlight(
                xpath="//input[@id='kw']",
                style_type="success",
                label_text="搜索框验证通过"
            )
            logger.info("✅ 搜索框验证通过并已高亮")
        except Exception as e:
            logger.error(f"❌ 搜索框验证失败: {e}")
        
        # 2. 等待并高亮元素
        logger.info("等待并高亮搜索按钮")
        element = self.wait_and_highlight_element(
            xpath="//input[@id='su']",
            timeout=10,
            style_type="primary",
            label_text="搜索按钮已就绪"
        )
        
        if element:
            logger.info("✅ 搜索按钮等待成功并已高亮")
        else:
            logger.error("❌ 搜索按钮等待失败")
        
        # 3. 查找并高亮元素（不使用等待）
        logger.info("直接查找并高亮百度Logo")
        logo_element = self.find_element_by_xpath_with_highlight(
            xpath="//img[@src='//www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png']",
            style_type="info",
            label_text="百度Logo"
        )
        
        if logo_element:
            logger.info("✅ 百度Logo查找成功并已高亮")
        else:
            logger.warning("⚠️ 百度Logo查找失败，可能xpath需要更新")
        
        # 等待观察效果
        self.sleep(3)
        
        # 最终清除所有高亮
        self.clear_all_highlights()
        logger.info("✅ 已清除所有高亮效果")
    
    @pytest.mark.skip(reason="手动测试用例，需要用户交互")
    def test_manual_xpath_testing(self):
        """手动xpath测试（需要用户交互）"""
        logger.info("--- 手动xpath测试模式 ---")
        
        # 打开指定页面
        test_url = "https://www.baidu.com"
        self.open(test_url)
        self.sleep(2)
        
        logger.info(f"已打开测试页面: {test_url}")
        logger.info("您可以在控制台中手动测试xpath高亮功能")
        
        # 这里可以添加交互式测试逻辑
        # 实际使用时可以通过pytest参数或环境变量控制
        
        # 示例：测试常见元素
        common_xpaths = [
            "//input[@id='kw']",
            "//input[@id='su']", 
            "//a[contains(text(), '新闻')]"
        ]
        
        for xpath in common_xpaths:
            result = self.highlight_element_by_xpath(
                xpath=xpath,
                style_type="info",
                label_text=f"测试: {xpath}"
            )
            logger.info(f"测试xpath: {xpath} - {'成功' if result['success'] else '失败'}")
            self.sleep(1)
        
        # 保持页面打开以便观察
        self.sleep(10)
