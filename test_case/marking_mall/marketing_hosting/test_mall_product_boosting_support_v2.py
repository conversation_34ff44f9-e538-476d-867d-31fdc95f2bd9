from time import sleep
from unittest import skip

from selenium.webdriver.common.by import By
from ..base import BaseTestCase


@skip
class TestMallProductBoostingSupportV2(BaseTestCase):

    # 登陆 商品助推打爆
    def test_enter_product_boosting_support(self):
        self.login(domain="PRODUCT_BOOSTING_DOMAIN", account="gaohuzhen")

    # 校验标题文案
    def test_title_text(self):
        self.test_enter_product_boosting_support()
        self.assert_text("商品助推打爆", '//*[@id="root"]/div/div/div/div/div/div[1]/div[1]/div/div/span')
        self.assert_text("一亿+曝光扶持", "//span[contains(text(),'一亿+曝光扶持')]")
        self.assert_text("曝光率100%+", "//span[contains(text(),'曝光率100%+')]")
        self.assert_text(
            "通过助推（完成10%）->打爆（100%）两个阶段流量扶持，打造货架爆款商品！曝光率最高猛增100%，不拍视频，不直播也能轻松起量，助力商品飞速成长！",
            "//span[contains(text(),'通过助推（完成10%）')]")
        self.assert_text("活动范围：30日内新发布，且主要在商城货架售卖、商品卡订单成交较低的新品",
                         "//span[contains(text(),'活动范围：')]")
        self.assert_text("符合活动范围新品", "//div[contains(text(),'符合活动范围新品')]")
        self.assert_text("不符合范围新品", "//div[contains(text(),'不符合范围新品')]")

    # 校验 详细规则 跳链
    def test_detail_rule_jump_link(self):
        xpath = "//span[contains(text(),'详细规则')]"
        expected_url = 'https://docs.qingque.cn/d/home/<USER>'
        expected_text = "详细规则"

        self.check_jump_link(xpath, expected_text, expected_url, self.test_enter_product_boosting_support,
                             need_login=True, has_href=False, need_click=True)

    # 校验 查看机会商品 跳链
    def test_check_opportunity_goods_jump_link(self):
        xpath = "//span[contains(text(),'查看机会商品')]"
        expected_url = 'https://s.kwaixiaodian.com/zone/business/center/tianhe-chance-goods'
        expected_text = "查看机会商品"

        self.check_jump_link(xpath, expected_text, expected_url, self.test_enter_product_boosting_support,
                             need_login=True, has_href=False, need_click=True)

    # 校验 查看扶持规则 跳链
    def test_check_support_rule_jump_link(self):
        xpath = "//span[contains(text(),'查看扶持规则')]"
        expected_url = 'https://docs.qingque.cn/d/home/<USER>'
        expected_text = "查看扶持规则"

        self.check_jump_link(xpath, expected_text, expected_url, self.test_enter_product_boosting_support,
                             need_login=True, has_href=False, need_click=True)

    # 校验 扶持阶段
    def test_support_stage(self):
        self.test_enter_product_boosting_support()
        goods_xpath = '//*[@class="kwaishop-tianhe-product_boosting-pc-table-tbody"]/tr'

        exp_tab_names = ['可扶持商品', '扶持中', '已完成']
        for exp_tab_name in exp_tab_names:
            xpath = f"//span[contains(text(), '{exp_tab_name}')]"
            tab = self.find_element(xpath)
            tab.click()
            sleep(1)

            # tab文案校验
            tab = self.find_element(xpath)
            text = tab.text[:-1]
            tab_name, tab_num = text.split('(')
            print(tab_name, tab_num)
            assert tab_name == exp_tab_name, f'tab_name={tab_name}, exp_tab_name={exp_tab_name}'

            # tab商品数量校验
            tab_num = int(tab_num)
            if tab_num == 0:
                print('无商品数据')
                continue

            pagination_li_elements = (self.find_element("//*[contains(text(), '共')]")
                                      .find_element(By.XPATH, './..')
                                      .find_elements(By.XPATH, 'li'))
            # 获取商品总数
            total_goods_cnt = int(pagination_li_elements[0].text.split('共')[1].split('条')[0])
            assert tab_num == total_goods_cnt, f'tab_num={tab_num}, total_goods_cnt={total_goods_cnt}'

            # 获取每页展示商品数
            goods_cnt_per_page = pagination_li_elements[-1].text.split('条')[0]

            # 点击最后一页
            last_page = pagination_li_elements[-3]
            page_nums = int(last_page.text)
            print(f'最后一页: {page_nums}')
            last_page.click()
            self.sleep(1)

            # 获取最后一页商品数
            goods_list_elements = self.find_elements(goods_xpath)
            last_page_nums = len(goods_list_elements) - 1
            assert last_page_nums > 0, f'last_page_nums={last_page_nums}'
            print(f'最后一页商品数: {last_page_nums}')

            # 校验商品总数
            tab_goods_num = (page_nums - 1) * int(goods_cnt_per_page) + last_page_nums
            print(f'展示商品数: {tab_goods_num}')
            assert total_goods_cnt == tab_goods_num, f'total_goods_cnt={total_goods_cnt}, tab_goods_num={tab_goods_num}'

    # 校验 符合活动范围新品tab
    def test_matched_goods_tab(self):
        self.test_enter_product_boosting_support()

        self.assert_text("重 置", "//span[contains(text(),'重 置')]")
        self.assert_text("查 询", "//span[contains(text(),'查 询')]")

        exp_title_names = ["商品信息", "首次上架时间", "上新天数", "扶持阶段", "扶持状态", "成长建议", "30日新品期内商品卡数据"]
        thead_element = self.find_element('//*[@class="kwaishop-tianhe-product_boosting-pc-table-thead"]')
        th_elements = thead_element.find_elements(By.XPATH, './tr/th')
        title_names = [th_element.text for th_element in th_elements]
        print(title_names)
        assert title_names[:len(exp_title_names)] == exp_title_names
