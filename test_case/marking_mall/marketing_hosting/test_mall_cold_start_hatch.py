from time import sleep

from selenium.webdriver.common.by import By
from ..base import BaseTestCase
from unittest import skip, skipIf
from selenium.common.exceptions import TimeoutException, NoSuchElementException


@skip
class TestMallColdStartHatch(BaseTestCase):

    # 登陆冷启孵化
    def test_enter_cold_start_hatch(self):
        self.login(domain="COLD_START_DOMAIN", account="gaohuzhen")

    # 校验背景文案
    @skip
    def test_background_text(self):
        self.test_enter_cold_start_hatch()
        self.assert_text("新品免费起量", "//*[@id='dilu_micro_root']/div/div/div/div/div[1]/div[1]/div/div/span")
        self.assert_text("一亿+曝光扶持", "//span[contains(text(),'一亿+曝光扶持')]")
        self.assert_text("曝光率100%+", "//span[contains(text(),'曝光率100%+')]")
        self.assert_text("符合条件的新品将免费获得平台流量扶持，不发短视频不直播也能轻松起量，助力新品快速成长。",
                         "//span[contains(text(),'符合条件的新品将免费获得平台流量扶持')]")

    # 查看详细规则 跳链
    def test_detailed_rules_jump_link(self):
        xpath = "//a[contains(text(),'查看详细规则')]"
        expected_url = "https://docs.qingque.cn/d/home/<USER>"
        expected_text = "查看详细规则"
        self.check_jump_link(xpath, expected_text, expected_url, self.test_enter_cold_start_hatch,
                             need_login=True, has_href=False, need_click=True)

    # 查看扶持规则 跳链
    def test_support_rules_jump_link(self):
        xpath = "//a[contains(text(),'查看扶持规则')]"
        expected_url = "https://docs.qingque.cn/d/home/<USER>"
        expected_text = "查看扶持规则"
        self.check_jump_link(xpath, expected_text, expected_url, self.test_enter_cold_start_hatch,
                             need_login=True, has_href=False, need_click=True)

    # 查看扶持完成典型案例 跳链
    def test_classic_case_jump_link(self):
        xpath = "//a[contains(text(),'查看扶持完成典型案例')]"
        expected_url = "https://docs.qingque.cn/d/home/<USER>"
        expected_text = "查看扶持完成典型案例"
        self.check_jump_link(xpath, expected_text, expected_url, self.test_enter_cold_start_hatch,
                             need_login=True, has_href=False, need_click=True)

    # tab相关信息
    def test_tab_info(self):
        exp_tab_names = ['可扶持新品', '扶持中', '已完成']
        goods_xpath = '//*[@id="tableId"]/div/div[2]/table/tbody/tr'

        self.test_enter_cold_start_hatch()
        for exp_tab_name in exp_tab_names:
            tab = self.find_element(f"//*[contains(text(), '{exp_tab_name}')]")
            tab.click()
            sleep(1)

            # tab文案校验
            text = tab.text.split('\n')[0]
            tab_name, tab_num = text[:-1].split('(')
            print(tab_name, tab_num)
            assert tab_name == exp_tab_name, f'tab_name={tab_name}, exp_tab_name={exp_tab_name}'

            # tab商品数量校验
            tab_num = int(tab_num)
            if tab_num == 0:
                print('无商品数据')
                continue

            pagination_li_elements = (self.find_element("//*[contains(text(), '共')]")
                                      .find_element(By.XPATH, './..')
                                      .find_elements(By.XPATH, 'li'))
            # 获取商品总数
            total_goods_cnt = int(pagination_li_elements[0].text.split('共')[1].split('条')[0])
            assert tab_num == total_goods_cnt, f'tab_num={tab_num}, total_goods_cnt={total_goods_cnt}'

            # 获取每页展示商品数
            goods_cnt_per_page = pagination_li_elements[-1].text.split('条')[0]

            # 点击最后一页
            last_page = pagination_li_elements[-3]
            page_nums = int(last_page.text)
            print(f'最后一页: {page_nums}')
            last_page.click()
            self.sleep(1)

            # 获取最后一页商品数
            goods_list_elements = self.find_elements(goods_xpath)
            last_page_nums = len(goods_list_elements) - 1
            assert last_page_nums > 0, f'last_page_nums={last_page_nums}'
            print(f'最后一页商品数: {last_page_nums}')

            # 校验商品总数
            tab_goods_num = (page_nums - 1) * int(goods_cnt_per_page) + last_page_nums
            print(f'展示商品数: {tab_goods_num}')
            assert total_goods_cnt == tab_goods_num, f'total_goods_cnt={total_goods_cnt}, tab_goods_num={tab_goods_num}'

    # 扶持中的品，成长建议展示 报名活动 和 设置营销工具
    def test_growth_advice_in_support(self):
        text_2_links_mapping = {
            "报名活动": [
                '//*[@id="tableId"]/div/div[2]/table/tbody/tr[2]/td[5]/div/div/span[1]',
                'https://s.kwaixiaodian.com/zone/business-activity/business/list'
            ],
            "设置营销工具": [
                '//*[@id="tableId"]/div/div[2]/table/tbody/tr[2]/td[5]/div/div/span[2]',
                'https://s.kwaixiaodian.com/zone/marketing/tools/all-tools?from=kwaixiaodian_market_pc'
            ]
        }
        self.test_enter_cold_start_hatch()

        # 切换到「扶持中」tab
        tab = self.find_element("//*[contains(text(), '扶持中')]")
        tab.click()
        sleep(1)
        text = tab.text.split('\n')[0]
        goods_cnt = text[:-1].split('(')[1]
        if goods_cnt == '0':
            print('已完成为0')
            return

        for expected_text, links in text_2_links_mapping.items():
            xpath, expected_url = links
            self.check_jump_link(xpath, expected_text, expected_url, self.test_enter_cold_start_hatch,
                                 need_login=False, has_href=False, need_click=True)

    # 查看详情弹窗
    @skip
    def test_view_detail(self):
        xpath = '//*[@id="tableId"]/div/div[2]/table/tbody/tr[2]/td[6]/div/div/span[4]'
        self.test_enter_cold_start_hatch()

        expose_cnt = self.get_text('//*[@id="tableId"]/div/div[2]/table/tbody/tr[2]/td[6]/div/div/span[1]')
        order_cnt = self.get_text('//*[@id="tableId"]/div/div[2]/table/tbody/tr[2]/td[6]/div/div/span[2]')
        pay_cnt = self.get_text('//*[@id="tableId"]/div/div[2]/table/tbody/tr[2]/td[6]/div/div/span[3]')

        self.check_jump_link(xpath, "查看详情", "", self.test_enter_cold_start_hatch,
                             need_login=False, has_href=False, need_click=True)

        expected_expose_cnt = self.get_text("/html/body/div[4]/div/div[2]/div/div/div[2]/div[2]/div[2]/div[1]/div[2]/span")
        expected_order_cnt = self.get_text("/html/body/div[4]/div/div[2]/div/div/div[2]/div[2]/div[2]/div[2]/div[2]/span")
        expected_pay_cnt = self.get_text("/html/body/div[4]/div/div[2]/div/div/div[2]/div[2]/div[2]/div[3]/div[2]/span")

        self.assert_equal(expose_cnt, '曝光量:' + expected_expose_cnt)
        self.assert_equal(order_cnt, '成交订单:' + expected_order_cnt)
        self.assert_equal(pay_cnt, '成交金额(元):' + expected_pay_cnt)
        print(expected_expose_cnt, expected_order_cnt, expected_pay_cnt)

    # 新品未入池的品，成长建议展示查看入池要求
    def test_growth_advice_in_new(self):
        self.test_enter_cold_start_hatch()

        # 等待页面加载完毕
        self.wait_for_ready_state_complete()

        # 验证文本和xpath准备
        expected_state_text = "新品未入池"
        expected_advice_text = "查看入池要求"
        pagination_ul_xpath = "//*[@id='root']/div[1]/div/div/div/div/div[2]/div[2]/div[1]/div/div[2]/div/div/div/div/ul"
        first_new_state_product_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[td[4]/div[1]/span[1][contains(text(), "{expected_state_text}")]][1]'

        try:
            # 步骤一：切换到可扶持新品tab下的最后一页
            # 等待和获取页码器元素
            self.wait_for_element_visible(pagination_ul_xpath)
            pagination_ul_element = self.find_element(pagination_ul_xpath)
            # 获取所有页码元素li
            pagination_li_elements = pagination_ul_element.find_elements(By.TAG_NAME, "li")
            # 获取并点击最后一页元素
            if len(pagination_li_elements) >= 3:  # 确保li元素的数量至少为3
                # 获取倒数第三个li元素,也就是尾页元素
                third_last_li = pagination_li_elements[-3]
                # 点击尾页元素
                third_last_li.click()
                sleep(3)
            else:
                print("Not enough page li elements found.")
                # assert False

            # 步骤二：获取到尾页第一个新品未入池的商品，并查看成长建议是否展示查看入池要求
            # 等待切换页面后的第一个孵化状态为新品未入池的商品元素
            self.wait_for_element_visible(first_new_state_product_xpath)
            # 验证是否能获取到新品未入池的商品元素
            if self.assert_text(expected_state_text, first_new_state_product_xpath):
                # 获取页面中第一个孵化状态为新品未入池的商品元素
                first_new_state_product_element = self.find_element(first_new_state_product_xpath)

                # 获取第一个新品未入池的商品下所有的td元素
                td_elements = first_new_state_product_element.find_elements(By.TAG_NAME, "td")

                # 获取第五个td字段，即成长建议字段
                if len(td_elements) >= 5:  # 确保至少有五个td元素
                    growth_advice_td_element = td_elements[4]  # 列表索引从0开始，所以第五个元素是索引4
                    growth_advice_btn = growth_advice_td_element.find_element(By.TAG_NAME, "span")
                    print('新品未入池-成长建议：', growth_advice_btn.text)

                    # assert growth_advice_btn.text == expected_advice_text

                else:
                    print("Less than five list fields elements found.")
                    # assert False
            else:
                # 表示尾页没有孵化状态为新品未入池的商品
                print("There are no products in the hatch state of new products that have not yet entered the pool.")
                # assert False

        except TimeoutException:
            print("Timeout: Element did not become visible within the specified time.")
            # assert False
        except NoSuchElementException:
            print("No such element: The element does not exist in the DOM.")
            # assert False
        except Exception as e:
            print("An unexpected error occurred:", str(e))
            # assert False

    # 新品未入池的品，成长建议查看入池要求是否弹窗
    def test_growth_advice_in_new_pop_win(self):
        self.test_enter_cold_start_hatch()

        # 等待页面加载完毕
        self.wait_for_ready_state_complete()

        # 验证文本和xpath准备
        expected_state_text = "新品未入池"
        expected_advice_text = "查看入池要求"
        expected_advice_detail_text = "去配券"
        pagination_ul_xpath = "//*[@id='root']/div[1]/div/div/div/div/div[2]/div[2]/div[1]/div/div[2]/div/div/div/div/ul"
        first_new_state_product_xpath = f'//*[@id="tableId"]/div/div[2]/table/tbody/tr[td[4]/div[1]/span[1][contains(text(), "{expected_state_text}")]][1]'
        # 弹窗元素
        growth_advice_pop_xpath = '//*[@id="root"]/div/div/div/div/div/div[2]/div[2]/div[2]/div/div/div/div/div[2]'

        try:
            # 步骤一：切换到可扶持新品tab下的最后一页
            # 等待和获取页码器元素
            self.wait_for_element_visible(pagination_ul_xpath)
            pagination_ul_element = self.find_element(pagination_ul_xpath)
            # 获取所有页码元素li
            pagination_li_elements = pagination_ul_element.find_elements(By.TAG_NAME, "li")
            # 获取并点击最后一页元素
            if len(pagination_li_elements) >= 3:  # 确保li元素的数量至少为3
                # 获取倒数第三个li元素,也就是尾页元素
                third_last_li = pagination_li_elements[-3]
                # 点击尾页元素
                third_last_li.click()
                sleep(3)
            else:
                print("Not enough page li elements found.")
                # assert False

            # 步骤二：获取到尾页第一个新品未入池的商品，并点击成长建议的查看入池要求
            # 等待切换页面后的第一个孵化状态为新品未入池的商品元素
            self.wait_for_element_visible(first_new_state_product_xpath)
            # 验证是否能获取到新品未入池的商品元素
            if self.assert_text(expected_state_text, first_new_state_product_xpath):
                # 获取页面中第一个孵化状态为新品未入池的商品元素
                first_new_state_product_element = self.find_element(first_new_state_product_xpath)

                # 获取第一个新品未入池的商品下所有的td元素
                td_elements = first_new_state_product_element.find_elements(By.TAG_NAME, "td")

                # 获取第五个td字段，即成长建议字段
                if len(td_elements) >= 5:  # 确保至少有五个td元素
                    growth_advice_td_element = td_elements[4]  # 列表索引从0开始，所以第五个元素是索引4
                    growth_advice_btn = growth_advice_td_element.find_element(By.TAG_NAME, "span")

                    growth_advice_btn.click()
                    sleep(2)

                else:
                    print("Less than five list fields elements found.")
                    # assert False
            else:
                # 表示尾页没有孵化状态为新品未入池的商品
                print("There are no products in the hatch state of new products that have not yet entered the pool.")
                # assert False

            # 步骤三：验证查看入池要求弹窗是否正常显示
            # 等待点击查看入池要求后的弹窗元素是否存在
            self.wait_for_element_visible(growth_advice_pop_xpath)
            # 验证弹窗元素中的去配券元素是否存在
            self.assert_text(expected_advice_detail_text, growth_advice_pop_xpath)
            sleep(5)
        except TimeoutException:
            print("Timeout: Element did not become visible within the specified time.")
            # assert False
        except NoSuchElementException:
            print("No such element: The element does not exist in the DOM.")
            # assert False
        except Exception as e:
            print("An unexpected error occurred:", str(e))
            # assert False

    # 已出坡的品，成长建议展示报名活动和设置营销工具
    def test_growth_advice_in_released(self):
        self.test_enter_cold_start_hatch()

        # 等待页面加载完毕
        self.wait_for_ready_state_complete()

        # 验证文本和xpath准备
        expected_state_text = "已完成"
        expected_advice_text1 = "报名活动"
        expected_advice_text2 = "设置营销工具"
        released_tab_xpath = "//*[@id='root']/div/div/div/div/div/div[2]/div[1]/div/div[3]/div[1]"
        released_num_xpath = "//*[@id='root']/div[1]/div/div/div/div/div[2]/div[1]/div/div[3]/div[1]/span[1]"
        first_released_state_product_xpath = '//*[@id="tableId"]/div/div[2]/table/tbody/tr[2]'

        try:
            # 步骤一：判断已完成商品数量是否为0
            self.wait_for_element_visible(released_num_xpath)
            real_num_text = self.find_element(released_num_xpath).text
            print('已完成商品数量:', real_num_text)
            if '已完成(0)' == real_num_text:
                print('已完成商品数量为0')
                # assert False

            # 步骤二：切换到已出坡tab
            # 等待和获取页码器元素
            self.wait_for_element_visible(released_tab_xpath)
            self.click(released_tab_xpath)

            # 步骤三：获取到第一个已出坡的商品，并查看是否展示报名活动和设置营销工具
            # 等待切换页面后的第一个孵化状态为新品未入池的商品元素
            self.wait_for_element_visible(first_released_state_product_xpath)
            # 验证是否能获取到已出坡的商品元素
            if self.assert_text(expected_state_text, first_released_state_product_xpath):
                # 获取页面中第一个孵化状态为已出坡的商品元素
                first_released_state_product_element = self.find_element(first_released_state_product_xpath)

                # 获取第一个已出坡的商品下所有的td元素
                td_elements = first_released_state_product_element.find_elements(By.TAG_NAME, "td")

                # 获取第五个td字段，即成长建议字段
                if len(td_elements) >= 5:  # 确保至少有五个td元素
                    growth_advice_td_element = td_elements[4]  # 列表索引从0开始，所以第五个元素是索引4
                    growth_advice_spans = growth_advice_td_element.find_elements(By.TAG_NAME, "span")

                    if len(growth_advice_spans) == 2:
                        # assert growth_advice_spans[0].text == expected_advice_text1
                        print(growth_advice_spans[0].text)
                        # assert growth_advice_spans[1].text == expected_advice_text2
                        print(growth_advice_spans[1].text)
                    else:
                        print("Less than two list fields elements found.")
                        # assert False
                else:
                    print("Less than five list fields elements found.")
                    # assert False
            else:
                # 表示尾页没有孵化状态为已出坡的商品
                print("There are no products in the hatch state of new products that have not yet entered the pool.")
                # assert False
        except TimeoutException:
            print("Timeout: Element did not become visible within the specified time.")
            # assert False
        except NoSuchElementException:
            print("No such element: The element does not exist in the DOM.")
            # assert False
        except Exception as e:
            print("An unexpected error occurred:", str(e))
            # assert False
