"""
# Author     ：author yuanpan
# Description：
"""
import time
from time import sleep
import datetime

import pytest as pytest
from ddt import ddt
from selenium.webdriver.common.by import By

from utils.account_help import get_account_detail
from .base import BaseTestCase
from unittest import skip, skipIf
from constant.domain import get_domain
from constant.account import get_account_info, ACCOUNT_CONFIG_ONLINE
from constant.domain import get_domain
from utils.account_help import get_account_detail
from utils.csv_help import get_csv_data
from utils.env_help import get_env
# from test_case.core_link.helper_assistant.test_scan_QRcode_login import testQRCodeLogin
# testQRCodeLogin : testQRCodeLogin()

# 系列活动列表链接
INVESTMENT_BUSINESS_LIST = 'https://s.kwaixiaodian.com/zone/business-invitation/business/list'
# 子活动列表链接
INVESTMENT_BUSINESS_SINGLELIST = 'https://s.kwaixiaodian.com/zone/business-invitation/business/singleList?id=1248'
# 已报活动链接
INVESTMENT_RECORD_LIST = 'https://s.kwaixiaodian.com/zone/business-invitation/record/list'
# 活动详情
INVESTMENT_BUSINESS_DETAIL = 'https://s.kwaixiaodian.com/zone/business-invitation/business/detail?id=15412'
# 商品托管链接
INVESTMENT_PRODUCTHOST_MANAGE = 'https://s.kwaixiaodian.com/zone/business-activity/productHost/manage'
# 爆款竞价页面
INVESTMENT_COMPETECOMMODITY_LIST = 'https://s.kwaixiaodian.com/zone/business-invitation/competeCommodity/list'
#官方竞价
INVESTMENT_OFFICIAL_BIDDING_LIST = 'https://s.kwaixiaodian.com/zone/business-invitation/hyperlinkBidding/list'
#商城必报活动
INVESTMENT_MALL_ACTIVITIES = 'https://s.kwaixiaodian.com/zone/market-hosting/homepage'
#招商最复杂提报撤回提报重新提报
INVESTMENT_BUSINESS_SINGLELIST2 = 'https://s.kwaixiaodian.com/zone/business-invitation/business/detail?id=19690&entry_src=PC_M_SHARE%2FNEW_REPORT%2FNEW_REPORT&version_type=1'
@ddt
class LoadInvestmentActivity(BaseTestCase):

    # 浏览器最大化，登陆招商页面
    def maximize_window_and_login(self):
        self.maximize_window()
        self.login("INVESTMENT_ACTIVITY_DOMAIN", "didengke")

    # 跳过新手引导
    def skip_guider(self):
        # 刷新当前页面
        self.refresh_page()
        # 【多退货地址策略】功能指引
        sleep(2)
        # self.click('//*[@id="driver-popover-item"]/div[4]/button')


    #系列活动列表链接
    def check_investment_business_list(self):

        sleep(2)
        self.open_url(INVESTMENT_BUSINESS_LIST)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.assert_no_broken_links()
        self.click('//*[@id="rc-tabs-0-tab-1"]')
        sleep(2)
        self.assert_text('查看活动','//*[@id="rc-tabs-0-panel-1"]/div/div/div/div[1]/div[1]/div[2]/div[2]/div/button/span')

    # 子活动列表链接
    def check_investment_business_singleList(self):
        sleep(2)
        self.open_url(INVESTMENT_BUSINESS_SINGLELIST)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.assert_no_broken_links()
        sleep(2)
        self.assert_text('招商测试-线上回测','//*[@id="root"]/section/section/main/div/div/div/div[1]/div[2]/div/div[1]/span[1]/span')


    # 已报活动链接
    def check_investment_record_list(self):
        sleep(2)
        self.open_url(INVESTMENT_RECORD_LIST)
        sleep(2)
        '判断页面没有报错'
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.assert_no_broken_links()
        sleep(2)
        self.assert_text('活动信息','//*[@id="rc-tabs-0-panel-0"]/div/div[2]/div/div/div/div/div/div/table/thead/tr/th[1]')




    # 活动详情
    def check_investment_business_detail(self):
        sleep(2)
        self.open_url(INVESTMENT_BUSINESS_DETAIL)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.assert_no_broken_links()
        sleep(2)
        self.assert_text('提报商品','//*[@id="root"]/section/section/main/div/div/div/div[1]/div[2]/div[1]/div/div[2]/div/div[3]/div[1]')


    # 商品托管链接
    def check_investment_productHost_manage(self):
        sleep(2)
        self.open_url(INVESTMENT_PRODUCTHOST_MANAGE)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.assert_no_broken_links()

        self.assert_text('添加托管商品','//*[@id="root"]/div/div/div/div[4]/div[2]/div[2]/div/div[1]/div/div/div/div/div[2]/div/div/span/button/span')
    # 爆款竞价页面-爆款商品列表
    def check_investment_comoeteCommodity_item_list(self):
        sleep(2)
        self.open_url(INVESTMENT_COMPETECOMMODITY_LIST)
        sleep(2)
        self.assert_no_404_errors()
        # self.assert_no_js_errors()
        self.assert_no_broken_links()
        sleep(3)
        self.assert_text('查 询','//*[@id="pro-form-wrapper"]/div[3]/div[3]/div/div/div[2]/button/span')



    # 爆款竞价页面-已报管理
    def check_investment_comoeteCommodity_record_list(self):
        sleep(2)
        self.open_url(INVESTMENT_COMPETECOMMODITY_LIST)
        sleep(2)
        self.assert_no_404_errors()
        # self.assert_no_js_errors()
        self.assert_no_broken_links()
        self.click('//*[@id="rc-tabs-0-tab-2"]')
        sleep(3)
        self.assert_text('查 询','//*[@id="pro-form-wrapper"]/div[5]/div[2]/div/div/div[2]/button/span')


    #提报抽屉
    def check_investment_comoeteCommodity_drawer_list(self):
        sleep(2)
        self.open_url(INVESTMENT_BUSINESS_DETAIL)
        sleep(2)
        self.assert_no_404_errors()
        # self.assert_no_js_errors()
        self.assert_no_broken_links()
        sleep(2)
        self.assert_text('可报名商品', '//*[@id="root"]/section/section/main/div/div/div/div[1]/div[2]/div[2]/div/div/div/div/div[1]/div/label[1]/span[2]')




    #官方竞价
    def check_investment_official_bidding(self):
        sleep(2)
        self.open_url(INVESTMENT_OFFICIAL_BIDDING_LIST)
        sleep(2)
        self.assert_no_404_errors()
        # self.assert_no_js_errors()
        self.assert_no_broken_links()
        sleep(5)
        self.click('//*[@id="rc-tabs-0-panel-1"]/div/div[2]/div/div/div[1]')
        sleep(5)
        self.assert_text('查 询','//*[@id="pro-form-wrapper"]/div[2]/div[4]/div/div/div[2]/button')



    def check_investment_bidding_registered(self):
        sleep(2)
        self.open_url(INVESTMENT_OFFICIAL_BIDDING_LIST)
        sleep(2)
        self.assert_no_404_errors()
        # self.assert_no_js_errors()
        self.assert_no_broken_links()
        sleep(2)
        self.click('//*[@id="rc-tabs-0-tab-2"]')
        sleep(2)
        self.assert_text('竞价商品信息','//*[@id="rc-tabs-0-panel-2"]/div/div[3]/div/div/div[2]/div/div/div/div/div/div[1]/table/thead/tr/th[1]')



    #添加托管商品页面展示
    def check_investment_add_commodity(self):
        sleep(2)
        self.open_url(INVESTMENT_PRODUCTHOST_MANAGE)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.assert_no_broken_links()
        sleep(2)
        self.click('//*[@id="root"]/div/div/div/div[4]/div[2]/div[2]/div/div[1]/div/div/div/div/div[2]/div/div/span/button/span')
        sleep(2)
        self.assert_text('添加托管商品','//*[@id="root"]/div/div/div/div[2]/div[1]')


    #查看托管详情
    def check_investment_look_particulars(self):
        sleep(2)
        self.open_url(INVESTMENT_PRODUCTHOST_MANAGE)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.assert_no_broken_links()
        sleep(2)
        self.click('//*[@id="root"]/div/div/div/div[4]/div[2]/div[2]/div/div[2]/div/div/div/div/div/div/div/table/tbody/tr[2]/td[11]/div/div[3]/a')



        sleep(2)
        self.assert_text('取 消','/html/div/div/div[2]/div/div/div[2]/div[2]/button/span')



    #商城必报活动页面（默认tab）
    def check_investment_mall_activities(self):
        sleep(2)
        self.open_url(INVESTMENT_MALL_ACTIVITIES)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.assert_no_broken_links()
        sleep(2)
        self.click('//*[@id="activity-panel-tab-1"]/span/span[2]')
        sleep(2)
        self.assert_text('货架零成本高效经营，尽享多项超优权益','//*[@id="activity-panel-panel-1"]/div/div[1]/div[1]')




    #商城必报活动页面（全部tab）
    def check_investment_mall_activities_all_tab(self):
        sleep(2)
        self.open_url(INVESTMENT_MALL_ACTIVITIES)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.assert_no_broken_links()
        sleep(2)
        self.click('//*[@id="activity-panel-tab-0"]/span')
        sleep(2)
        self.assert_text('报名时间排序','//*[@id="rc-tabs-0-panel-1"]/div/div[2]/div/div/form/div[2]/div/div/div/div/div/div[1]')


    #商城必报活动页面（第三个tab）
    def check_investment_mall_activities_three_tab(self):
        sleep(2)
        self.open_url(INVESTMENT_MALL_ACTIVITIES)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.assert_no_broken_links()
        sleep(2)
        self.click('//*[@id="activity-panel-tab-3"]/span/span')
        sleep(2)
        self.assert_text('平台重点扶持，助您持续爆单！','//*[@id="activity-panel-panel-3"]/div/div[1]/div[1]')


    #商城必报活动页面（第四个个tab）
    def check_investment_mall_activities_four_tab(self):
        sleep(2)
        self.open_url(INVESTMENT_MALL_ACTIVITIES)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.assert_no_broken_links()
        sleep(2)
        self.click('//*[@id="activity-panel-tab-4"]/span/span')
        sleep(2)

        self.assert_text('一键托管商品，快手卖货无忧愁！','//*[@id="activity-panel-panel-4"]/div/div[1]/div[1]')


    #商城必报活动页面（第五个tab）
    def check_investment_mall_activities_five_tab(self):
        sleep(2)
        self.open_url(INVESTMENT_MALL_ACTIVITIES)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.assert_no_broken_links()
        sleep(2)
        self.click('//*[@id="activity-panel-tab-2"]/span/span')
        sleep(2)
        self.assert_text('大促日促专区，助您销量冲高！','//*[@id="activity-panel-panel-2"]/div/div[1]/div[1]')


#商城必报活动页面（第六个tab）
    def check_investment_mall_activities_six_tab(self):
        sleep(2)
        self.open_url(INVESTMENT_MALL_ACTIVITIES)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.assert_no_broken_links()
        sleep(2)
        self.click('//*[@id="activity-panel-tab-6"]/span/span')
        sleep(2)
        self.assert_text('商城心智频道，超低门槛享流量扶持！','//*[@id="activity-panel-panel-6"]/div/div[1]/div[1]')



#商城必报活动页面（第七个tab）
    def check_investment_mall_activities_seven_tab(self):
        sleep(2)
        self.open_url(INVESTMENT_MALL_ACTIVITIES)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.assert_no_broken_links()
        sleep(2)
        self.click('//*[@id="activity-panel-tab-5"]/span/span')
        sleep(2)
        self.assert_text('平台特色玩法频道，低门槛拿官方补贴！','//*[@id="activity-panel-panel-5"]/div/div[1]/div[1]')


    #商城必报活动页面（提报记录页面）
    def check_investment_commodity_record(self):
        sleep(2)
        self.open_url(INVESTMENT_MALL_ACTIVITIES)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.assert_no_broken_links()
        sleep(2)
        self.click('//*[@id="activity-record"]')
        sleep(2)
        self.assert_text('商城营销托管','//*[@id="rc-tabs-0-panel-2"]/div/div[2]/form/div[1]/div[1]/div[2]/div/div/div/div/div[1]')



    #爆款竞价已报列表撤回重新提报
    def check_investment_already_apply_list(self):
        sleep(2)
        self.open_url(INVESTMENT_COMPETECOMMODITY_LIST)
        sleep(2)
        self.assert_no_404_errors()
        # self.assert_no_js_errors()
        self.assert_no_broken_links()
        sleep(2)
        #关闭触达弹窗
        #self.click('//*[@id="root"]/div/div/div/div/div/div[2]/div/div[2]/button/span')
        sleep(2)
        #点击已报管理tab
        self.click('//*[@id="rc-tabs-0-tab-2"]')
        sleep(2)
        #点击测试活动
        self.click('//*[@id="pro-form-wrapper"]/div[2]/div/div/div/div/div/div/div[2]/div/div/div/div/div[4]')
        sleep(2)
        #点击取消活动
        self.click('//*[@id="rc-tabs-0-panel-2"]/div/div/div[2]/div/div/div/div/div[2]/table/tbody/tr[2]/td[9]/div/button[2]/span')
        sleep(3)
        #二次确认弹窗
        self.click('/html/body/div[*]/div/div[2]/div/div[2]/div/div/div[2]/button[2]/span')
        sleep(5)
       #点击重新提报
        self.click('//*[@id="rc-tabs-0-panel-2"]/div/div/div[2]/div/div/div/div/div[2]/table/tbody/tr[2]/td[9]/div/button[2]/span')
        sleep(5)
        #拉起抽屉 确认提报
        self.click('/html/div/div/div[2]/div/div/div[2]/div[2]/div/div/div[2]/div[3]/button[2]/span')
        sleep(3)


   # 活动详情
    def check_investment_complex_activity(self):
        sleep(2)
        self.open_url(INVESTMENT_BUSINESS_SINGLELIST2 )
        sleep(2)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.assert_no_broken_links()
        sleep(2)
        #点击报名记录tab
        self.click('//*[@id="root"]/section/section/main/div/div/div/div[1]/div[2]/div[1]/div/div[4]/div/div[3]/div[1]')
        sleep(2)
        #点击撤回提报
        self.click('//*[@id="root"]/section/section/main/div/div/div/div[1]/div[2]/div[2]/div/div[2]/div/div/div/div/div[2]/table/tbody/tr[2]/td[11]/span/a[2]')
        sleep(5)
        #二次弹窗确认撤回提报
        self.click('/html/body/div[3]/div/div[2]/div/div[2]/div/div/div[2]/button[2]')
        sleep(5)
        #点击重新提报
        self.click('//*[@id="root"]/section/section/main/div/div/div/div[1]/div[2]/div[2]/div/div[2]/div/div/div/div/div[2]/table/tbody/tr[2]/td[11]/span/a[2]')
        sleep(5)
        #拉起抽屉 确认提报
        self.click('/html/body/div[4]/div/div[2]/div/div/div[2]/div[5]/button[2]/span')
        sleep(5)
        self.assert_text('待审核','//*[@id="root"]/section/section/main/div/div/div/div[1]/div[2]/div[2]/div/div[2]/div/div/div/div/div[2]/table/tbody/tr[2]/td[9]/span/span[2]')

    # 营销托管总览签署协议页面
    def check_investment_hosting_overview(self):
        sleep(2)
        self.open_url(INVESTMENT_MALL_ACTIVITIES)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.assert_no_broken_links()
        sleep(5)
        #点击托管总览
        self.click('//*[@id="menu_item_MV3gCEV75Ww"]')
        sleep(5)
        #签署协议页面
        self.assert_text('我已经阅读并同意','//*[@id="dilu_micro_root"]/div/div/div/div/div[1]/div[2]/div[2]/div/div/label/span[2]')

    # 营销托管首页(整体商城数据)模块
    def check_investment_hosting_overview_home_page(self):
        sleep(2)
        self.open_url(INVESTMENT_MALL_ACTIVITIES)
        sleep(2)
        self.assert_no_404_errors()
        self.assert_no_js_errors()
        self.assert_no_broken_links()
        sleep(5)
        #点击托管总览
        self.click('//*[@id="menu_item_MV3gCEV75Ww"]')
        sleep(5)
        #签署协议页面
        #self.click('//*[@id="dilu_micro_root"]/div/div/div/div/div[1]/div[2]/div[2]/div/div/label/span[2]')
        #sleep(2)
        #self.click('//*[@id="dilu_micro_root"]/div/div/div/div/div[1]/div[2]/div[2]/button/span')
        #sleep(5)
        self.assert_text('整体商城数据','//*[@id="dilu_micro_root"]/div/div/div/div/div[1]/div[1]/div[1]/div[1]')