"""
实用的XPath组件高亮器
支持多种定位策略和高亮样式
"""

import logging
import time
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PracticalXPathHighlighter:
    """实用的XPath组件高亮器"""
    
    def __init__(self, driver):
        self.driver = driver
        self.wait = WebDriverWait(driver, 10)
        
        # 高亮样式配置
        self.highlight_styles = {
            "success": {
                "border": "3px solid #28a745",
                "background": "rgba(40, 167, 69, 0.15)",
                "box-shadow": "0 0 15px rgba(40, 167, 69, 0.6)"
            },
            "warning": {
                "border": "3px solid #ffc107",
                "background": "rgba(255, 193, 7, 0.15)",
                "box-shadow": "0 0 15px rgba(255, 193, 7, 0.6)"
            },
            "error": {
                "border": "3px solid #dc3545",
                "background": "rgba(220, 53, 69, 0.15)",
                "box-shadow": "0 0 15px rgba(220, 53, 69, 0.6)"
            },
            "info": {
                "border": "3px solid #17a2b8",
                "background": "rgba(23, 162, 184, 0.15)",
                "box-shadow": "0 0 15px rgba(23, 162, 184, 0.6)"
            }
        }
        
        # 标签图标配置
        self.label_icons = {
            "success": "✅",
            "warning": "⚠️",
            "error": "❌",
            "info": "ℹ️"
        }
    
    def find_and_highlight_component(self, component_config: dict) -> dict:
        """
        根据配置查找并高亮组件
        
        Args:
            component_config: 组件配置，包含定位策略和样式信息
            
        Returns:
            操作结果
        """
        try:
            # 解析配置
            locators = component_config.get("locators", [])
            label = component_config.get("label", "未命名组件")
            style_type = component_config.get("style", "success")
            timeout = component_config.get("timeout", 10)
            
            element = None
            used_locator = None
            
            # 尝试不同的定位策略
            for locator in locators:
                locator_type = locator.get("type", "xpath")
                locator_value = locator.get("value", "")
                
                try:
                    if locator_type == "xpath":
                        element = WebDriverWait(self.driver, timeout).until(
                            EC.presence_of_element_located((By.XPATH, locator_value))
                        )
                    elif locator_type == "css":
                        element = WebDriverWait(self.driver, timeout).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, locator_value))
                        )
                    elif locator_type == "id":
                        element = WebDriverWait(self.driver, timeout).until(
                            EC.presence_of_element_located((By.ID, locator_value))
                        )
                    elif locator_type == "class":
                        element = WebDriverWait(self.driver, timeout).until(
                            EC.presence_of_element_located((By.CLASS_NAME, locator_value))
                        )
                    
                    if element:
                        used_locator = locator
                        break
                        
                except (TimeoutException, NoSuchElementException):
                    continue
            
            if not element:
                logger.warning(f"❌ 未找到组件: {label}")
                return {
                    "success": False,
                    "label": label,
                    "error": "未找到元素"
                }
            
            # 滚动到元素位置
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
            time.sleep(0.5)
            
            # 应用高亮样式
            self.apply_highlight_style(element, style_type)
            
            # 添加标签
            self.add_component_label(element, label, style_type, used_locator)
            
            logger.info(f"✅ 成功高亮组件: {label}")
            return {
                "success": True,
                "label": label,
                "locator_used": used_locator,
                "element_info": self.get_element_info(element)
            }
            
        except Exception as e:
            logger.error(f"❌ 高亮组件失败 {label}: {e}")
            return {
                "success": False,
                "label": label,
                "error": str(e)
            }
    
    def highlight_component_list(self, components: list) -> dict:
        """
        高亮组件列表
        
        Args:
            components: 组件配置列表
            
        Returns:
            批量操作结果
        """
        results = {
            "total": len(components),
            "success_count": 0,
            "failed_count": 0,
            "results": []
        }
        
        for i, component in enumerate(components):
            logger.info(f"正在处理组件 {i+1}/{len(components)}: {component.get('label', '未命名')}")
            
            result = self.find_and_highlight_component(component)
            results["results"].append(result)
            
            if result["success"]:
                results["success_count"] += 1
            else:
                results["failed_count"] += 1
            
            # 添加延迟，让用户看到高亮过程
            time.sleep(1)
        
        return results
    
    def apply_highlight_style(self, element, style_type: str):
        """应用高亮样式"""
        
        style_config = self.highlight_styles.get(style_type, self.highlight_styles["success"])
        
        # 构建样式字符串
        style_parts = []
        for property_name, value in style_config.items():
            style_parts.append(f"{property_name}: {value}")
        
        style_string = "; ".join(style_parts)
        
        # 保存原始样式
        original_style = element.get_attribute("style") or ""
        
        # 应用新样式
        new_style = f"{original_style}; {style_string}; position: relative; z-index: 9998;"
        
        self.driver.execute_script(
            "arguments[0].setAttribute('style', arguments[1]); "
            "arguments[0].setAttribute('data-original-style', arguments[2]); "
            "arguments[0].setAttribute('data-highlight-type', arguments[3]);",
            element, new_style, original_style, style_type
        )
    
    def add_component_label(self, element, label_text: str, style_type: str, locator_info: dict):
        """添加组件标签"""
        
        # 根据样式类型选择颜色和图标
        icon = self.label_icons.get(style_type, "🎯")
        
        color_map = {
            "success": "#28a745",
            "warning": "#ffc107",
            "error": "#dc3545",
            "info": "#17a2b8"
        }
        bg_color = color_map.get(style_type, "#28a745")
        text_color = "white" if style_type != "warning" else "black"
        
        # 构建定位器信息
        locator_text = ""
        if locator_info:
            locator_text = f" ({locator_info['type']}: {locator_info['value'][:30]}...)"
        
        label_html = f"""
        <div class="practical-highlight-label" style="
            position: absolute;
            top: -35px;
            left: 0;
            background: {bg_color};
            color: {text_color};
            padding: 6px 10px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: bold;
            z-index: 9999;
            white-space: nowrap;
            box-shadow: 0 3px 6px rgba(0,0,0,0.3);
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
        ">
            {icon} {label_text}{locator_text}
        </div>
        """
        
        # 插入标签
        self.driver.execute_script("""
            var element = arguments[0];
            var labelHtml = arguments[1];
            
            // 移除已存在的标签
            var existingLabels = element.querySelectorAll('.practical-highlight-label');
            existingLabels.forEach(function(label) {
                label.remove();
            });
            
            // 创建新标签
            var labelContainer = document.createElement('div');
            labelContainer.innerHTML = labelHtml;
            var label = labelContainer.firstElementChild;
            
            // 插入到元素中
            element.appendChild(label);
            
            // 确保元素有相对定位
            if (getComputedStyle(element).position === 'static') {
                element.style.position = 'relative';
            }
        """, element, label_html)
    
    def get_element_info(self, element) -> dict:
        """获取元素信息"""
        try:
            return {
                "tag_name": element.tag_name,
                "text": element.text[:100] if element.text else "",
                "id": element.get_attribute("id") or "",
                "class": element.get_attribute("class") or "",
                "location": element.location,
                "size": element.size
            }
        except:
            return {}
    
    def clear_all_highlights(self):
        """清除所有高亮"""
        try:
            self.driver.execute_script("""
                // 恢复原始样式
                var highlightedElements = document.querySelectorAll('[data-original-style]');
                highlightedElements.forEach(function(element) {
                    var originalStyle = element.getAttribute('data-original-style');
                    element.setAttribute('style', originalStyle);
                    element.removeAttribute('data-original-style');
                    element.removeAttribute('data-highlight-type');
                });
                
                // 移除所有标签
                var labels = document.querySelectorAll('.practical-highlight-label');
                labels.forEach(function(label) {
                    label.remove();
                });
            """)
            
            logger.info("✅ 已清除所有高亮")
            
        except Exception as e:
            logger.error(f"❌ 清除高亮失败: {e}")
    
    def create_results_panel(self, results: dict):
        """创建结果面板"""
        
        success_rate = (results["success_count"] / results["total"] * 100) if results["total"] > 0 else 0
        
        # 构建详细结果列表
        details_html = ""
        for result in results["results"][:5]:  # 只显示前5个
            icon = "✅" if result["success"] else "❌"
            label = result["label"]
            details_html += f'<div style="margin: 2px 0; font-size: 11px;">{icon} {label}</div>'
        
        if len(results["results"]) > 5:
            details_html += f'<div style="font-size: 11px; color: #666;">... 还有 {len(results["results"]) - 5} 个</div>'
        
        panel_html = f"""
        <div id="practical-highlight-panel" style="
            position: fixed;
            top: 20px;
            right: 20px;
            width: 320px;
            background: white;
            border: 2px solid #007bff;
            border-radius: 10px;
            box-shadow: 0 6px 20px rgba(0,0,0,0.25);
            z-index: 10000;
            font-family: 'Segoe UI', Arial, sans-serif;
            font-size: 14px;
        ">
            <div style="
                background: linear-gradient(135deg, #007bff, #0056b3);
                color: white;
                padding: 15px;
                border-radius: 8px 8px 0 0;
                font-weight: bold;
                text-align: center;
            ">
                🎯 组件高亮结果
            </div>
            <div style="padding: 15px;">
                <div style="margin-bottom: 12px;">
                    <div style="color: #28a745; font-weight: bold; margin-bottom: 4px;">
                        ✅ 成功: {results['success_count']} / {results['total']}
                    </div>
                    <div style="color: #dc3545; font-weight: bold; margin-bottom: 4px;">
                        ❌ 失败: {results['failed_count']} / {results['total']}
                    </div>
                    <div style="color: #007bff; font-weight: bold;">
                        📊 成功率: {success_rate:.1f}%
                    </div>
                </div>
                
                <div style="border-top: 1px solid #dee2e6; padding-top: 10px; margin-bottom: 15px;">
                    <div style="font-weight: bold; margin-bottom: 6px; color: #333;">详细结果:</div>
                    {details_html}
                </div>
                
                <div style="text-align: center;">
                    <button onclick="clearPracticalHighlights()" style="
                        background: linear-gradient(135deg, #dc3545, #c82333);
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 13px;
                        font-weight: bold;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                    ">
                        🗑️ 清除所有高亮
                    </button>
                </div>
            </div>
        </div>
        
        <script>
            function clearPracticalHighlights() {{
                // 清除高亮
                var highlightedElements = document.querySelectorAll('[data-original-style]');
                highlightedElements.forEach(function(element) {{
                    var originalStyle = element.getAttribute('data-original-style');
                    element.setAttribute('style', originalStyle);
                    element.removeAttribute('data-original-style');
                    element.removeAttribute('data-highlight-type');
                }});
                
                // 移除所有标签
                var labels = document.querySelectorAll('.practical-highlight-label');
                labels.forEach(function(label) {{
                    label.remove();
                }});
                
                // 移除面板
                var panel = document.getElementById('practical-highlight-panel');
                if (panel) {{
                    panel.remove();
                }}
            }}
        </script>
        """
        
        try:
            self.driver.execute_script("""
                // 移除已存在的面板
                var existingPanel = document.getElementById('practical-highlight-panel');
                if (existingPanel) {
                    existingPanel.remove();
                }
                
                // 创建新面板
                var panel = document.createElement('div');
                panel.innerHTML = arguments[0];
                document.body.appendChild(panel.firstElementChild);
            """, panel_html)
            
            logger.info("✅ 已创建结果面板")
            
        except Exception as e:
            logger.error(f"❌ 创建结果面板失败: {e}")


def demo_practical_highlighting():
    """实用高亮演示"""
    
    # 设置Chrome驱动
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.maximize_window()
    
    try:
        logger.info("=== 实用XPath组件高亮演示 ===")
        
        # 打开测试页面
        test_url = "https://www.github.com"
        driver.get(test_url)
        logger.info(f"已打开页面: {test_url}")
        
        time.sleep(3)
        
        # 创建高亮器
        highlighter = PracticalXPathHighlighter(driver)
        
        # 定义要高亮的组件配置
        components = [
            {
                "label": "GitHub Logo",
                "style": "success",
                "locators": [
                    {"type": "xpath", "value": "//a[@aria-label='Homepage']"},
                    {"type": "css", "value": ".Header-link"},
                    {"type": "xpath", "value": "//svg[@class='octicon octicon-mark-github']"}
                ]
            },
            {
                "label": "搜索框",
                "style": "info",
                "locators": [
                    {"type": "xpath", "value": "//input[@placeholder='Search GitHub']"},
                    {"type": "css", "value": "input[type='search']"},
                    {"type": "xpath", "value": "//input[@name='q']"}
                ]
            },
            {
                "label": "登录按钮",
                "style": "warning",
                "locators": [
                    {"type": "xpath", "value": "//a[contains(text(), 'Sign in')]"},
                    {"type": "css", "value": ".HeaderMenu-link--sign-in"},
                    {"type": "xpath", "value": "//a[@href='/login']"}
                ]
            },
            {
                "label": "注册按钮",
                "style": "success",
                "locators": [
                    {"type": "xpath", "value": "//a[contains(text(), 'Sign up')]"},
                    {"type": "css", "value": ".HeaderMenu-link--sign-up"},
                    {"type": "xpath", "value": "//a[@href='/signup']"}
                ]
            }
        ]
        
        logger.info("开始高亮页面组件...")
        
        # 高亮组件
        results = highlighter.highlight_component_list(components)
        
        # 创建结果面板
        highlighter.create_results_panel(results)
        
        # 输出结果
        logger.info("=== 高亮完成 ===")
        logger.info(f"总计: {results['total']} 个组件")
        logger.info(f"成功: {results['success_count']} 个")
        logger.info(f"失败: {results['failed_count']} 个")
        
        logger.info("💡 请查看浏览器中的高亮效果和结果面板")
        
        input("按回车键结束演示...")
        
    except Exception as e:
        logger.error(f"演示失败: {e}")
        
    finally:
        driver.quit()


if __name__ == "__main__":
    demo_practical_highlighting()
